/**
 * 简化的音频播放器 - 通过WebRTC DataChannel接收PCM音频
 */

import { reactive } from 'vue'

// 音频状态
const audioState = reactive({
  connected: false,
  playing: false,
  volume: 1.0,
  error: null
})

// 音频上下文
let audioContext = null
let frameCount = 0

/**
 * 初始化音频播放器
 */
export async function initAudioPlayer() {
  try {
    // 创建音频上下文
    audioContext = new AudioContext({
      sampleRate: 44100,
      latencyHint: 'interactive'
    })

    // 确保音频上下文启动
    if (audioContext.state === 'suspended') {
      await audioContext.resume()
    }

    console.log(`✅ 音频播放器初始化成功 - 采样率: ${audioContext.sampleRate}Hz, 状态: ${audioContext.state}`)
    audioState.error = null
    return true
  } catch (error) {
    console.error('音频播放器初始化失败:', error)
    audioState.error = error.message
    return false
  }
}

/**
 * 设置音频DataChannel
 */
export function setupAudioDataChannel(audioChannel) {
  try {
    if (!audioContext) {
      console.error('🔊 音频上下文未初始化')
      return false
    }

    console.log('🔊 设置音频DataChannel')

    audioChannel.onmessage = (event) => {
      if (event.data instanceof ArrayBuffer && event.data.byteLength > 0) {
        handleAudioData(event.data)
      }
    }

    audioChannel.onopen = () => {
      console.log('🔊 音频DataChannel已打开')
      audioState.connected = true
    }

    audioChannel.onclose = () => {
      console.log('🔊 音频DataChannel已关闭')
      audioState.connected = false
    }

    audioChannel.onerror = (error) => {
      console.error('🔊 音频DataChannel错误:', error)
      audioState.error = 'DataChannel连接失败'
      audioState.connected = false
    }

    return true
  } catch (error) {
    console.error('设置音频DataChannel失败:', error)
    audioState.error = error.message
    return false
  }
}

/**
 * 处理接收到的音频数据
 */
function handleAudioData(arrayBuffer) {
  try {
    frameCount++
    
    // 跳过第一个字节（协议标识）
    const audioData = arrayBuffer.slice(1)
    
    // 转换为PCM数据
    const pcmData = new Int16Array(audioData)
    
    // 每100帧显示一次调试信息
    if (frameCount % 100 === 0) {
      console.log(`🔊 音频帧 #${frameCount}: ${pcmData.length} 样本`)
      console.log(`🔊 前8个样本: [${Array.from(pcmData.slice(0, 8)).join(', ')}]`)
    }

    // 检查数据有效性
    if (pcmData.length < 100) {
      return // 数据太小，跳过
    }

    // 创建单声道音频缓冲区
    const frameLength = pcmData.length / 2 // 假设立体声
    const audioBuffer = audioContext.createBuffer(1, frameLength, audioContext.sampleRate)
    const channelData = audioBuffer.getChannelData(0)
    
    // 将立体声混合为单声道
    for (let i = 0; i < frameLength; i++) {
      const left = pcmData[i * 2] || 0
      const right = pcmData[i * 2 + 1] || 0
      channelData[i] = (left + right) / 2 / 32768.0
    }

    // 立即播放
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)
    source.start()
    
    audioState.playing = true

  } catch (error) {
    console.error('🔊 处理音频数据失败:', error)
  }
}

/**
 * 停止音频播放
 */
export function stopAudioPlayer() {
  audioState.connected = false
  audioState.playing = false
  console.log('🔊 音频播放器已停止')
}

/**
 * 获取音频状态
 */
export function getAudioState() {
  return audioState
}
