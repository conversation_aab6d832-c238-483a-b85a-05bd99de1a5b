# 🎥 音视频流测试指南

## 📋 测试步骤

### 1. 启动后端服务器
```bash
cd webrtc-server/webrtc-server
# 重新编译项目
# 运行编译后的程序
```

### 2. 连接前端
- 打开浏览器访问 `http://localhost:8080`
- 点击"连接"按钮
- 确认WebRTC连接成功，DataChannel打开

### 3. 推流测试

#### 方法1: 使用FFmpeg推送测试视频
```bash
# 推送测试模式视频流 (彩色条纹 + 音频)
ffmpeg -f lavfi -i testsrc2=size=1920x1080:rate=30 -f lavfi -i sine=frequency=1000:sample_rate=48000 -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -f mpegts udp://127.0.0.1:1234

# 推送静态图片循环
ffmpeg -loop 1 -i your_image.jpg -f lavfi -i sine=frequency=440 -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -t 60 -f mpegts udp://127.0.0.1:1234
```

#### 方法2: 推送摄像头 (如果有摄像头)
```bash
# Windows (DirectShow)
ffmpeg -f dshow -i video="Your Camera Name":audio="Your Microphone Name" -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -f mpegts udp://127.0.0.1:1234

# Linux (V4L2)
ffmpeg -f v4l2 -i /dev/video0 -f alsa -i default -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -f mpegts udp://127.0.0.1:1234
```

#### 方法3: 推送桌面录制
```bash
# Windows (GDI grab)
ffmpeg -f gdigrab -i desktop -f dshow -i audio="立体声混音 (Realtek(R) Audio)" -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -f mpegts udp://127.0.0.1:1234

# 指定区域录制
ffmpeg -f gdigrab -offset_x 100 -offset_y 100 -video_size 1280x720 -i desktop -c:v libx264 -preset ultrafast -tune zerolatency -f mpegts udp://127.0.0.1:1234
```

#### 方法4: 推送视频文件
```bash
# 循环播放视频文件
ffmpeg -stream_loop -1 -i your_video.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -f mpegts udp://127.0.0.1:1234

# 转码并推送
ffmpeg -i input.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -c:a aac -f mpegts udp://127.0.0.1:1234
```

## 🔍 测试验证

### 后端日志检查
应该看到以下日志：
```
[FFmpegDecoder] FFmpeg decoder started, listening for UDP stream on 127.0.0.1:1234
[FFmpegDecoder] Stream opened successfully
[FFmpegDecoder] Video stream found: 1920x1080, H264
[FFmpegDecoder] Audio stream found: 48000Hz, AAC
[WebRTCPeer:xxx] Sending video frame: 1234 bytes
[WebRTCPeer:xxx] Sending audio frame: 567 bytes
```

### 前端验证
- 浏览器控制台应显示: "开始接收视频流"
- 视频画布应显示接收到的视频内容
- 视频统计信息应更新 (帧率、解码性能等)

## 🛠️ 故障排除

### 1. 无法接收流
- 检查UDP端口1234是否被占用
- 确认防火墙设置
- 验证FFmpeg命令语法

### 2. 视频不显示
- 检查浏览器WebCodecs支持
- 查看前端控制台错误
- 确认H264编码参数

### 3. 音频问题
- 检查AAC编码设置
- 验证音频采样率匹配

## 📊 性能优化建议

### FFmpeg编码参数
```bash
# 低延迟优化
-preset ultrafast -tune zerolatency -profile:v baseline -level 3.1

# 质量平衡
-preset fast -tune zerolatency -crf 23

# 带宽限制
-b:v 2M -maxrate 2M -bufsize 1M
```

### 分辨率调整
```bash
# 降低分辨率减少带宽
-s 1280x720    # 720p
-s 854x480     # 480p
-s 640x360     # 360p
```

## 🎯 测试目标

1. ✅ 确认视频流接收和解码
2. ✅ 验证音频流传输
3. ✅ 测试实时性能 (延迟 < 100ms)
4. ✅ 检查帧率稳定性
5. ✅ 验证长时间运行稳定性

开始测试吧！🚀
