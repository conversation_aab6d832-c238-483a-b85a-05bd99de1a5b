#pragma once

#include <memory>
#include <string>
#include <functional>
#include <atomic>
#include <mutex>
#include <rtc/rtc.hpp>


class WebRTCPeer {
public:
    using InputEventCallback = std::function<void(const std::string&)>;

    explicit WebRTCPeer(const std::string& id);
    ~WebRTCPeer();

    const std::string& getId() const { return m_id; }
    bool isConnected() const { return m_connected; }
    bool handleOffer(const std::string& offer);
    std::string getAnswer() const { return m_answer; }
    void addIceCandidate(const std::string& candidate);

    void sendVideoFrame(const uint8_t* data, size_t size);
    void sendAudioFrame(const uint8_t* data, size_t size);

    void setInputEventCallback(InputEventCallback callback) { m_inputEventCallback = callback; }
    void setConnected(bool connected) { m_connected = connected; }

private:
    void setupPeerConnection();
    void setupDataChannels();
    void setupMediaTracks();
    
    void onDataChannelOpen(std::shared_ptr<rtc::DataChannel> channel);
    void onDataChannelMessage(std::shared_ptr<rtc::DataChannel> channel, const std::string& message);
    void onConnectionStateChange(rtc::PeerConnection::State state);

    std::string m_id;
    std::atomic<bool> m_connected{false};
    std::string m_answer;


    std::shared_ptr<rtc::PeerConnection> m_peerConnection;
    std::shared_ptr<rtc::DataChannel> m_inputChannel;
    std::shared_ptr<rtc::DataChannel> m_videoChannel;
    std::shared_ptr<rtc::DataChannel> m_audioChannel;

    std::shared_ptr<rtc::Track> m_videoTrack;
    std::shared_ptr<rtc::Track> m_audioTrack;

    InputEventCallback m_inputEventCallback;

    mutable std::mutex m_mutex;
};
