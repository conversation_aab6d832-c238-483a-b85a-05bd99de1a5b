import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useWebRTCStore = defineStore('webrtc', () => {
  // 连接状态
  const connected = ref(false)
  const connecting = ref(false)
  const connectionStatus = ref('未连接')
  const dataChannelOpen = ref(false)
  const error = ref(null)

  // 服务器配置
  const serverUrl = ref('http://localhost:8080')

  // 视频状态
  const videoReceived = ref(false)
  const videoStats = ref({
    resolution: '等待视频流...',
    fps: 0,
    totalFrames: 0,
    decodedFrames: 0,
    droppedFrames: 0
  })

  // 输入控制
  const inputEnabled = ref(true)

  // 日志
  const logs = ref([])

  // 计算属性
  const isReady = computed(() => connected.value && dataChannelOpen.value && videoReceived.value)
  const connectionQuality = computed(() => {
    if (!connected.value) return 'disconnected'
    if (videoStats.value.droppedFrames > videoStats.value.totalFrames * 0.1) return 'poor'
    if (videoStats.value.fps < 20) return 'fair'
    return 'good'
  })
  
  // Actions
  function updateConnectionState(state) {
    connected.value = state.connected || false
    connecting.value = state.connecting || false
    connectionStatus.value = state.status || '未连接'
    dataChannelOpen.value = state.dataChannelOpen || false
    error.value = state.error || null
  }

  function updateVideoStats(stats) {
    videoStats.value = { ...videoStats.value, ...stats }
  }

  function setVideoReceived(received) {
    videoReceived.value = received
  }

  function setInputEnabled(enabled) {
    inputEnabled.value = enabled
  }

  function addLog(type, message) {
    const timestamp = new Date().toLocaleTimeString()
    logs.value.unshift({
      time: timestamp,
      type: type,
      message: message
    })

    // 限制日志数量
    if (logs.value.length > 100) {
      logs.value = logs.value.slice(0, 100)
    }
  }

  function clearLogs() {
    logs.value = []
  }

  function resetStats() {
    videoStats.value = {
      resolution: '等待视频流...',
      fps: 0,
      totalFrames: 0,
      decodedFrames: 0,
      droppedFrames: 0
    }
    videoReceived.value = false
    clearLogs()
  }
  
  return {
    // 状态
    connected,
    connecting,
    connectionStatus,
    dataChannelOpen,
    error,
    serverUrl,
    videoReceived,
    videoStats,
    inputEnabled,
    logs,

    // 计算属性
    isReady,
    connectionQuality,

    // 方法
    updateConnectionState,
    updateVideoStats,
    setVideoReceived,
    setInputEnabled,
    addLog,
    clearLogs,
    resetStats
  }
})
