#include "AudioWebSocketServer.h"
#include <iostream>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <algorithm>
#include <thread>
#include <chrono>

#pragma comment(lib, "ws2_32.lib")

AudioWebSocketServer::AudioWebSocketServer() {
    std::cout << "[AudioWebSocketServer] Creating audio WebSocket server" << std::endl;
}

AudioWebSocketServer::~AudioWebSocketServer() {
    stop();
}

bool AudioWebSocketServer::start(int port) {
    if (m_running) {
        std::cout << "[AudioWebSocketServer] Server already running" << std::endl;
        return true;
    }

    m_port = port;

    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "[AudioWebSocketServer] WSAStartup failed" << std::endl;
        return false;
    }

    m_serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (m_serverSocket == INVALID_SOCKET) {
        std::cerr << "[AudioWebSocketServer] Failed to create socket" << std::endl;
        WSACleanup();
        return false;
    }

    int opt = 1;
    setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));

    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    addr.sin_addr.s_addr = INADDR_ANY;

    if (bind(m_serverSocket, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
        std::cerr << "[AudioWebSocketServer] Failed to bind to port " << port << std::endl;
        closesocket(m_serverSocket);
        WSACleanup();
        return false;
    }

    if (listen(m_serverSocket, 5) == SOCKET_ERROR) {
        std::cerr << "[AudioWebSocketServer] Failed to listen" << std::endl;
        closesocket(m_serverSocket);
        WSACleanup();
        return false;
    }

    m_running = true;
    m_serverThread = std::thread(&AudioWebSocketServer::serverLoop, this);

    std::cout << "[AudioWebSocketServer] Server started on port " << port << std::endl;
    return true;
}

void AudioWebSocketServer::stop() {
    if (!m_running) return;

    std::cout << "[AudioWebSocketServer] Stopping server..." << std::endl;
    m_running = false;

    if (m_serverSocket != -1) {
        closesocket(m_serverSocket);
        m_serverSocket = -1;
    }

    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        for (int client : m_clients) {
            closesocket(client);
        }
        m_clients.clear();
    }

    if (m_serverThread.joinable()) {
        m_serverThread.join();
    }

    WSACleanup();
    std::cout << "[AudioWebSocketServer] Server stopped" << std::endl;
}

void AudioWebSocketServer::sendAudioData(const uint8_t* data, size_t size) {
    if (!m_running || size == 0) return;

    static int audioPacketCount = 0;
    audioPacketCount++;

    if (audioPacketCount % 100 == 0) {
        std::cout << "[AudioWebSocketServer] Sending audio packet " << audioPacketCount
                  << " (size: " << size << " bytes, clients: " << m_clientCount << ")" << std::endl;
    }

    {
        std::lock_guard<std::mutex> lock(m_audioQueueMutex);

        while (m_audioQueue.size() >= MAX_QUEUE_SIZE) {
            m_audioQueue.pop();
        }

        std::vector<uint8_t> audioData(data, data + size);
        m_audioQueue.push(std::move(audioData));
    }

    std::vector<uint8_t> audioData(data, data + size);
    broadcastAudioData(audioData);
}

void AudioWebSocketServer::serverLoop() {
    std::cout << "[AudioWebSocketServer] Server loop started" << std::endl;

    while (m_running) {
        sockaddr_in clientAddr;
        int clientAddrLen = sizeof(clientAddr);
        
        int clientSocket = accept(m_serverSocket, (sockaddr*)&clientAddr, &clientAddrLen);
        
        if (clientSocket == INVALID_SOCKET) {
            if (m_running) {
                std::cerr << "[AudioWebSocketServer] Accept failed" << std::endl;
            }
            continue;
        }

        std::cout << "[AudioWebSocketServer] New client connected" << std::endl;
        
        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            m_clients.push_back(clientSocket);
            m_clientCount = m_clients.size();
        }

        std::thread clientThread(&AudioWebSocketServer::handleClient, this, clientSocket);
        clientThread.detach();
    }

    std::cout << "[AudioWebSocketServer] Server loop ended" << std::endl;
}

void AudioWebSocketServer::handleClient(int clientSocket) {
    std::cout << "[AudioWebSocketServer] Handling client connection" << std::endl;

    char buffer[1024];
    
    while (m_running) {
        int bytesReceived = recv(clientSocket, buffer, sizeof(buffer), 0);
        
        if (bytesReceived <= 0) {
            break; 
        }

        std::string request(buffer, bytesReceived);
        if (request.find("GET") == 0 && request.find("Upgrade: websocket") != std::string::npos) {
            std::string response = 
                "HTTP/1.1 101 Switching Protocols\r\n"
                "Upgrade: websocket\r\n"
                "Connection: Upgrade\r\n"
                "Sec-WebSocket-Accept: dummy\r\n"
                "\r\n";
            
            send(clientSocket, response.c_str(), response.length(), 0);
            std::cout << "[AudioWebSocketServer] WebSocket handshake completed" << std::endl;
            break;
        }
    }

    u_long mode = 1;
    ioctlsocket(clientSocket, FIONBIO, &mode);

    std::cout << "[AudioWebSocketServer] Client connection established, keeping alive..." << std::endl;
    while (m_running) {
        char dummy[1];
        int result = recv(clientSocket, dummy, 1, MSG_PEEK);
        if (result == SOCKET_ERROR) {
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK) {
                std::cout << "[AudioWebSocketServer] Client connection lost (error: " << error << ")" << std::endl;
                break;
            }
        } else if (result == 0) {
            std::cout << "[AudioWebSocketServer] Client gracefully closed connection" << std::endl;
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        m_clients.erase(std::remove(m_clients.begin(), m_clients.end(), clientSocket), m_clients.end());
        m_clientCount = m_clients.size();
    }

    closesocket(clientSocket);
    std::cout << "[AudioWebSocketServer] Client disconnected (remaining clients: " << m_clientCount << ")" << std::endl;
}

void AudioWebSocketServer::broadcastAudioData(const std::vector<uint8_t>& data) {
    if (data.empty()) return;

    std::lock_guard<std::mutex> lock(m_clientsMutex);
    
    std::vector<uint8_t> frame;
    frame.push_back(0x82); // FIN=1, Opcode=2 (binary)
    
    if (data.size() < 126) {
        frame.push_back(static_cast<uint8_t>(data.size()));
    } else if (data.size() < 65536) {
        frame.push_back(126);
        frame.push_back((data.size() >> 8) & 0xFF);
        frame.push_back(data.size() & 0xFF);
    } else {
        return;
    }
    
    frame.insert(frame.end(), data.begin(), data.end());

    for (auto it = m_clients.begin(); it != m_clients.end();) {
        int result = send(*it, reinterpret_cast<const char*>(frame.data()), frame.size(), 0);
        if (result == SOCKET_ERROR) {
            closesocket(*it);
            it = m_clients.erase(it);
        } else {
            ++it;
        }
    }
    
    m_clientCount = m_clients.size();
}
