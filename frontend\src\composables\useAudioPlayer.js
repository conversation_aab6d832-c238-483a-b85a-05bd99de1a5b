/**
 * 🔊 独立音频播放器
 * 通过WebSocket或HTTP接收PCM音频流并播放
 */

import { ref, reactive } from 'vue'

// 音频状态
const audioState = reactive({
  connected: false,
  playing: false,
  volume: 1.0,
  sampleRate: 44100, // 匹配后端的采样率
  channels: 2,
  error: null,
  websocket: null
})

// AAC帧缓冲区
let aacBuffer = new Uint8Array(0)

// 统计信息
let frameStats = {
  total: 0,
  success: 0,
  failed: 0
}

// 音频上下文和节点
let audioContext = null
let audioWorkletNode = null
let websocket = null

/**
 * 初始化音频播放器
 */
export async function initAudioPlayer() {
  try {
    // 创建音频上下文
    audioContext = new (window.AudioContext || window.webkitAudioContext)({
      sampleRate: 44100,
      latencyHint: 'interactive'
    })

    // 确保音频上下文启动
    if (audioContext.state === 'suspended') {
      await audioContext.resume()
    }

    console.log(`✅ 音频播放器初始化成功 - 采样率: ${audioContext.sampleRate}Hz, 状态: ${audioContext.state}`)

    // 每5秒检查音频上下文状态
    setInterval(() => {
      if (frameStats.total > 0) {
        console.log(`🔊 音频上下文状态: ${audioContext.state}, 当前时间: ${audioContext.currentTime.toFixed(3)}秒`)
      }
    }, 5000)

    audioState.error = null
    return true
  } catch (error) {
    console.error('音频播放器初始化失败:', error)
    audioState.error = error.message
    return false
  }
}

/**
 * 设置音频DataChannel - 使用WebRTC
 */
export function setupAudioDataChannel(audioChannel) {
  try {
    if (!audioContext) {
      console.error('🔊 音频上下文未初始化')
      return false
    }

    console.log('🔊 设置音频DataChannel')
    audioState.error = null

    // 设置DataChannel事件处理
    audioChannel.onmessage = (event) => {
      if (event.data instanceof ArrayBuffer && event.data.byteLength > 0) {
        handleAudioData(event.data)
      }
    }

    audioChannel.onopen = () => {
      console.log('🔊 音频DataChannel已打开')
      audioState.connected = true
    }

    audioChannel.onclose = () => {
      console.log('🔊 音频DataChannel已关闭')
      audioState.connected = false
    }

    audioChannel.onerror = (error) => {
      console.error('🔊 音频DataChannel错误:', error)
      audioState.error = 'DataChannel连接失败'
      audioState.connected = false
    }

    return true
  } catch (error) {
    console.error('设置音频DataChannel失败:', error)
    audioState.error = error.message
    return false
  }
}

async function startWebSocketAudioStream(url) {
  return new Promise((resolve, reject) => {
    try {
      const ws = new WebSocket(url)
      ws.binaryType = 'arraybuffer'

      ws.onopen = () => {
        console.log('🔊 音频WebSocket连接成功')
        audioState.connected = true
        audioState.websocket = ws
        resolve()
      }

      ws.onmessage = async (event) => {
        if (event.data instanceof ArrayBuffer && event.data.byteLength > 0) {
          handleAudioData(event.data)
        }
      }

      ws.onclose = () => {
        console.log('🔊 音频WebSocket连接关闭')
        audioState.connected = false
        audioState.websocket = null
      }

      ws.onerror = (error) => {
        console.error('🔊 音频WebSocket错误:', error)
        audioState.connected = false
        audioState.websocket = null
        audioState.error = 'WebSocket连接错误'
        reject(error)
      }

    } catch (error) {
      console.error('🔊 创建WebSocket失败:', error)
      audioState.error = error.message
      reject(error)
    }
  })
}

/**
 * 处理音频数据
 */
function handleAudioData(arrayBuffer) {
  try {
    if (!audioContext || audioContext.state !== 'running') {
      return
    }

    console.log(`🔊 收到PCM音频数据: ${arrayBuffer.byteLength} 字节`)

    // 检查数据大小是否合理
    if (arrayBuffer.byteLength === 0) {
      console.warn('🔊 音频数据为空')
      return
    }

    // 检查数据大小是否为4的倍数 (16bit * 2声道)
    if (arrayBuffer.byteLength % 4 !== 0) {
      console.warn('🔊 PCM数据大小不是4的倍数:', arrayBuffer.byteLength)
      return
    }

    // 直接处理PCM数据
    processPCMData(arrayBuffer)

  } catch (error) {
    console.error('🔊 处理音频数据失败:', error)
    audioState.error = error.message
  }
}

// 音频播放队列和时间管理
let audioQueue = []
let nextPlayTime = 0
let isPlaying = false

function processPCMData(arrayBuffer) {
  try {
    // 简单直接的PCM处理 - 假设Little Endian，立体声
    const pcmData = new Int16Array(arrayBuffer)
    const frameCount = pcmData.length / 2 // 立体声，每帧2个样本

    frameStats.total++

    // 每50个包显示一次调试信息
    if (frameStats.total % 50 === 0) {
      console.log(`🔊 PCM包 #${frameStats.total}: ${pcmData.length} 样本, ${frameCount} 帧`)

      // 显示前8个样本
      const samples = Array.from(pcmData.slice(0, 8))
      console.log(`🔊 原始样本: [${samples.join(', ')}]`)

      // 检查信号强度
      const maxAmp = Math.max(...samples.map(Math.abs))
      console.log(`🔊 最大振幅: ${maxAmp}`)
    }

    // 创建单声道音频缓冲区（简化处理）
    const audioBuffer = audioContext.createBuffer(1, frameCount, audioState.sampleRate)
    const channelData = audioBuffer.getChannelData(0)

    // 将立体声混合为单声道
    for (let i = 0; i < frameCount; i++) {
      const leftSample = pcmData[i * 2] || 0
      const rightSample = pcmData[i * 2 + 1] || 0
      // 混合左右声道
      channelData[i] = (leftSample + rightSample) / 2 / 32768.0
    }

    // 立即播放，不使用复杂的调度
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)
    source.start()

    frameStats.success++
    audioState.playing = true

  } catch (error) {
    frameStats.total++
    frameStats.failed++
    console.error('🔊 PCM处理失败:', error)
  }
}



/**
 * 断开音频流
 */
export function disconnectAudioStream() {
  audioState.connected = false
  audioState.playing = false
  console.log('音频流已断开')

  if (audioState.websocket) {
    audioState.websocket.close()
    audioState.websocket = null
  }
}

/**
 * 设置音量
 */
export function setVolume(volume) {
  audioState.volume = Math.max(0, Math.min(1, volume))
}

/**
 * 获取音频状态
 */
export function getAudioState() {
  return audioState
}

/**
 * 清理音频播放器
 */
export function cleanupAudioPlayer() {
  disconnectAudioStream()
  
  if (audioContext) {
    audioContext.close()
    audioContext = null
  }
  
  console.log('音频播放器已清理')
}
