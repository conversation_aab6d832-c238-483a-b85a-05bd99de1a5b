﻿  FFmpegDecoder.cpp
D:\vcpkg\installed\x64-windows\include\libavutil\rational.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“FFmpegDecoder.cpp”)
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(553,76): error C2059: 语法错误:“{”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(553,76): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(553,76): error C2059: 语法错误:“,”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(533,5): error C2317: 在行“533”上开始的“try”块没有 catch 处理程序
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(553,100): error C2059: 语法错误:“)”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(554,13): error C2065: “ret”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(563,9): error C2065: “ret”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(563,50): error C2065: “codec”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(564,13): error C2065: “ret”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(566,25): error C2065: “ret”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(572,9): error C2065: “ret”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(573,36): error C2059: 语法错误:“{”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(573,36): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(573,36): error C2059: 语法错误:“,”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(573,60): error C2059: 语法错误:“,”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(574,36): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(574,36): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(574,60): error C2059: 语法错误:“,”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(575,45): error C2059: 语法错误:“)”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(576,9): error C2059: 语法错误:“if”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(576,22): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(576,22): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(581,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(581,24): error C2065: “m_aacSwrContext”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(582,9): error C2059: 语法错误:“if”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(582,22): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(582,22): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(590,9): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(590,21): error C3613: “->”后缺少返回类型(假定为“int”)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(590,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(590,21): error C2146: 语法错误: 缺少“;”(在标识符“nb_samples”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(591,9): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(591,21): error C3613: “->”后缺少返回类型(假定为“int”)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(591,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(591,9): error C2086: “int m_aacFrame”: 重定义
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(590,9):
      参见“m_aacFrame”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(591,21): error C2146: 语法错误: 缺少“;”(在标识符“format”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(592,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(592,9): error C2374: “ret”: 重定义；多次初始化
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(581,9):
      参见“ret”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(592,9): error C2086: “int ret”: 重定义
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(581,9):
      参见“ret”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(592,63): error C2065: “m_aacEncoderContext”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(592,15): error C2660: “av_channel_layout_copy”: 函数不接受 1 个参数
      D:\vcpkg\installed\x64-windows\include\libavutil\channel_layout.h(572,5):
      参见“av_channel_layout_copy”的声明
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(592,15):
      尝试匹配参数列表“()”时
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(593,9): error C2059: 语法错误:“if”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(593,22): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(593,22): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(597,9): error C3927: "->": 非函数声明符后不允许尾随返回类型
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(597,21): error C3613: “->”后缺少返回类型(假定为“int”)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(597,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(597,9): error C2086: “int m_aacFrame”: 重定义
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(590,9):
      参见“m_aacFrame”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(597,21): error C2146: 语法错误: 缺少“;”(在标识符“sample_rate”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(599,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(599,9): error C2374: “ret”: 重定义；多次初始化
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(581,9):
      参见“ret”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(599,9): error C2086: “int ret”: 重定义
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(581,9):
      参见“ret”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(599,15): error C2664: “int av_frame_get_buffer(AVFrame *,int)”: 无法将参数 1 从“int”转换为“AVFrame *”
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(599,35):
      从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
      D:\vcpkg\installed\x64-windows\include\libavutil\frame.h(896,5):
      参见“av_frame_get_buffer”的声明
      D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(599,15):
      尝试匹配参数列表“(int, int)”时
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(600,9): error C2059: 语法错误:“if”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(600,22): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(600,22): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(607,19): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(607,14): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(607,14): error C2371: “std::cout”: 重定义；不同的基类型
      C:\vs2022\VC\Tools\MSVC\14.44.35207\include\iostream(40,75):
      参见“std::cout”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(607,19): error C2059: 语法错误:“<<”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(608,19): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(608,14): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(608,14): error C2371: “std::cout”: 重定义；不同的基类型
      C:\vs2022\VC\Tools\MSVC\14.44.35207\include\iostream(40,75):
      参见“std::cout”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(608,19): error C2059: 语法错误:“<<”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(609,9): error C2059: 语法错误:“return”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(611,5): error C2059: 语法错误:“}”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(611,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(613,9): error C2059: 语法错误:“return”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(614,5): error C2059: 语法错误:“}”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(614,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(615,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(615,1): error C2059: 语法错误:“}”
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(617,79): error C2143: 语法错误: 缺少“;”(在“{”的前面)
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(617,79): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
