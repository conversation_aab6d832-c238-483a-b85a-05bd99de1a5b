﻿  FFmpegDecoder.cpp
D:\vcpkg\installed\x64-windows\include\libavutil\rational.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“FFmpegDecoder.cpp”)
  
  正在生成代码
  Previous IPDB and IOBJ mismatch, fall back to full compilation.
  All 6286 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  webrtc-server.vcxproj -> D:\webrtc-demo\webrtc-server\x64\Release\webrtc-server.exe
