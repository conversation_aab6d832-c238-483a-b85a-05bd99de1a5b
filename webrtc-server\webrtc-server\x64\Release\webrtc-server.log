﻿  FFmpegDecoder.cpp
D:\vcpkg\installed\x64-windows\include\libavutil\rational.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“FFmpegDecoder.cpp”)
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(554,26): error C2039: "channels": 不是 "AVCodecContext" 的成员
      D:\vcpkg\installed\x64-windows\include\libavcodec\avcodec.h(451,16):
      参见“AVCodecContext”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(555,26): error C2039: "channel_layout": 不是 "AVCodecContext" 的成员
      D:\vcpkg\installed\x64-windows\include\libavcodec\avcodec.h(451,16):
      参见“AVCodecContext”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(567,23): error C3861: “swr_alloc_set_opts”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(587,17): error C2039: "channels": 不是 "AVFrame" 的成员
      D:\vcpkg\installed\x64-windows\include\libavutil\frame.h(389,16):
      参见“AVFrame”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(587,49): error C2039: "channels": 不是 "AVCodecContext" 的成员
      D:\vcpkg\installed\x64-windows\include\libavcodec\avcodec.h(451,16):
      参见“AVCodecContext”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(588,17): error C2039: "channel_layout": 不是 "AVFrame" 的成员
      D:\vcpkg\installed\x64-windows\include\libavutil\frame.h(389,16):
      参见“AVFrame”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(588,55): error C2039: "channel_layout": 不是 "AVCodecContext" 的成员
      D:\vcpkg\installed\x64-windows\include\libavcodec\avcodec.h(451,16):
      参见“AVCodecContext”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\FFmpegDecoder.cpp(611,31): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
