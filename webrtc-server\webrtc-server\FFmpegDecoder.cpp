#include "FFmpegDecoder.h"
#include <iostream>
#include <chrono>
#include <winsock2.h>
#include <ws2tcpip.h>

#pragma comment(lib, "ws2_32.lib")

FFmpegDecoder::FFmpegDecoder()
    : m_formatContext(nullptr)
    , m_videoCodecContext(nullptr)
    , m_audioCodecContext(nullptr)
    , m_videoStreamIndex(-1)
    , m_audioStreamIndex(-1)
    , m_swsContext(nullptr)
    , m_swrContext(nullptr)
    , m_videoFrame(nullptr)
    , m_audioFrame(nullptr)
    , m_videoBuffer(nullptr)
    , m_audioBuffer(nullptr)
    , m_aacEncoderContext(nullptr)
    , m_aacFrame(nullptr)
    , m_aacSwrContext(nullptr) {

    std::cout << "[FFmpegDecoder] Creating FFmpeg decoder" << std::endl;
}

FFmpegDecoder::~FFmpegDecoder() {
    cleanup();
}

bool FFmpegDecoder::initialize() {
    std::cout << "[FFmpegDecoder] Initializing FFmpeg decoder..." << std::endl;

    try {
        // Initialize FFmpeg
        av_log_set_level(AV_LOG_WARNING);

        // Allocate frame buffers
        m_videoFrame = av_frame_alloc();
        m_audioFrame = av_frame_alloc();
        m_aacFrame = av_frame_alloc();

        if (!m_videoFrame || !m_audioFrame || !m_aacFrame) {
            std::cerr << "[FFmpegDecoder] Failed to allocate frame buffers" << std::endl;
            return false;
        }

        // Initialize AAC encoder
        if (!initializeAACEncoder()) {
            std::cerr << "[FFmpegDecoder] Failed to initialize AAC encoder" << std::endl;
            return false;
        }

        m_initialized = true;
        std::cout << "[FFmpegDecoder] FFmpeg decoder initialized successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void FFmpegDecoder::cleanup() {
    std::cout << "[FFmpegDecoder] Cleaning up FFmpeg decoder..." << std::endl;
    
    stopReceiving();
    cleanupDecoders();
    
    if (m_videoFrame) {
        av_frame_free(&m_videoFrame);
    }
    if (m_audioFrame) {
        av_frame_free(&m_audioFrame);
    }
    if (m_aacFrame) {
        av_frame_free(&m_aacFrame);
    }
    if (m_videoBuffer) {
        av_free(m_videoBuffer);
    }
    if (m_audioBuffer) {
        av_free(m_audioBuffer);
    }
    
    m_initialized = false;
    std::cout << "[FFmpegDecoder] FFmpeg decoder cleanup complete" << std::endl;
}

bool FFmpegDecoder::startReceiving(const std::string& inputUrl) {
    if (m_receiving) {
        std::cout << "[FFmpegDecoder] Already receiving stream" << std::endl;
        return true;
    }

    if (!m_initialized) {
        std::cerr << "[FFmpegDecoder] Decoder not initialized" << std::endl;
        return false;
    }

    m_inputUrl = inputUrl;
    std::cout << "[FFmpegDecoder] Starting to receive stream: " << m_inputUrl << std::endl;

    try {
        if (!openInputStream(m_inputUrl)) {
            return false;
        }

        m_receiving = true;
        m_receiveThread = std::thread(&FFmpegDecoder::receiveLoop, this);
        
        std::cout << "[FFmpegDecoder] Started receiving stream" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Failed to start receiving: " << e.what() << std::endl;
        return false;
    }
}

void FFmpegDecoder::stopReceiving() {
    if (!m_receiving) return;

    std::cout << "[FFmpegDecoder] Stopping receiving stream..." << std::endl;
    m_receiving = false;

    if (m_receiveThread.joinable()) {
        m_receiveThread.join();
    }

    closeInputStream();
    std::cout << "[FFmpegDecoder] Stopped receiving stream" << std::endl;
}

bool FFmpegDecoder::startAudioReceiving(const std::string& audioUrl) {
    if (m_audioReceiving) {
        std::cout << "[FFmpegDecoder] Already receiving audio stream" << std::endl;
        return true;
    }

    if (!m_initialized) {
        std::cerr << "[FFmpegDecoder] Decoder not initialized" << std::endl;
        return false;
    }

    std::cout << "[FFmpegDecoder] Starting to receive audio stream: " << audioUrl << std::endl;

    try {
        m_audioReceiving = true;
        m_audioReceiveThread = std::thread(&FFmpegDecoder::audioReceiveLoop, this, audioUrl);

        std::cout << "[FFmpegDecoder] Audio receiving started successfully" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Failed to start audio receiving: " << e.what() << std::endl;
        return false;
    }
}

void FFmpegDecoder::stopAudioReceiving() {
    if (!m_audioReceiving) return;

    std::cout << "[FFmpegDecoder] Stopping audio receiving stream..." << std::endl;
    m_audioReceiving = false;

    if (m_audioReceiveThread.joinable()) {
        m_audioReceiveThread.join();
    }

    std::cout << "[FFmpegDecoder] Stopped audio receiving stream" << std::endl;
}

void FFmpegDecoder::update() {
    if (m_initialized && !m_receiving && !m_inputUrl.empty()) {
        static auto lastRetryTime = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();

        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastRetryTime).count() >= 5) {
            lastRetryTime = now;
            std::cout << "[FFmpegDecoder] Attempting to reconnect to stream: " << m_inputUrl << std::endl;
            startReceiving(m_inputUrl);
        }
    }
}

bool FFmpegDecoder::openInputStream(const std::string& url) {
    try {
        m_formatContext = avformat_alloc_context();
        if (!m_formatContext) {
            std::cerr << "[FFmpegDecoder] Failed to allocate format context " << url << std::endl;
            return false;
        }

        AVDictionary* options = nullptr;
        av_dict_set(&options, "timeout", "5000000", 0); 

        int ret = avformat_open_input(&m_formatContext, url.c_str(), nullptr, &options);
        av_dict_free(&options);

        if (ret < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(ret, errbuf, sizeof(errbuf));
            std::cerr << "[FFmpegDecoder] Failed to open input stream: " << errbuf << std::endl;
            return false;
        }

        ret = avformat_find_stream_info(m_formatContext, nullptr);
        if (ret < 0) {
            std::cerr << "[FFmpegDecoder] Failed to find stream information" << std::endl;
            return false;
        }

        for (unsigned int i = 0; i < m_formatContext->nb_streams; i++) {
            AVCodecParameters* codecpar = m_formatContext->streams[i]->codecpar;
            
            if (codecpar->codec_type == AVMEDIA_TYPE_VIDEO && m_videoStreamIndex == -1) {
                m_videoStreamIndex = i;
                std::cout << "[FFmpegDecoder] Found video stream: " << i << std::endl;
            } else if (codecpar->codec_type == AVMEDIA_TYPE_AUDIO && m_audioStreamIndex == -1) {
                m_audioStreamIndex = i;
                std::cout << "[FFmpegDecoder] Found audio stream: " << i << std::endl;
            }
        }

        if (m_videoStreamIndex >= 0) {
            if (!initializeVideoDecoder()) {
                return false;
            }
        }

        if (m_audioStreamIndex >= 0) {
            if (!initializeAudioDecoder()) {
                return false;
            }
        }

        std::cout << "[FFmpegDecoder] Input stream opened successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Failed to open input stream: " << e.what() << std::endl;
        return false;
    }
}

void FFmpegDecoder::closeInputStream() {
    cleanupDecoders();
    
    if (m_formatContext) {
        avformat_close_input(&m_formatContext);
        m_formatContext = nullptr;
    }
    
    m_videoStreamIndex = -1;
    m_audioStreamIndex = -1;
}

bool FFmpegDecoder::initializeVideoDecoder() {
    if (m_videoStreamIndex < 0) return false;

    try {
        AVCodecParameters* codecpar = m_formatContext->streams[m_videoStreamIndex]->codecpar;
        const AVCodec* codec = avcodec_find_decoder(codecpar->codec_id);
        
        if (!codec) {
            std::cerr << "[FFmpegDecoder] Failed to find video decoder: " << avcodec_get_name(codecpar->codec_id) << std::endl;
            return false;
        }

        m_videoCodecContext = avcodec_alloc_context3(codec);
        if (!m_videoCodecContext) {
            std::cerr << "[FFmpegDecoder] Failed to allocate video codec context" << std::endl;
            return false;
        }

        int ret = avcodec_parameters_to_context(m_videoCodecContext, codecpar);
        if (ret < 0) {
            std::cerr << "[FFmpegDecoder] Failed to copy video codec parameters to context" << std::endl;
            return false;
        }

        ret = avcodec_open2(m_videoCodecContext, codec, nullptr);
        if (ret < 0) {
            std::cerr << "[FFmpegDecoder] Failed to open video codec" << std::endl;
            return false;
        }

        std::cout << "[FFmpegDecoder] Initialized video decoder: " << avcodec_get_name(codecpar->codec_id) << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Failed to initialize video decoder: " << e.what() << std::endl;
        return false;
    }
}

bool FFmpegDecoder::initializeAudioDecoder() {
    if (m_audioStreamIndex < 0) return false;

    try {
        AVCodecParameters* codecpar = m_formatContext->streams[m_audioStreamIndex]->codecpar;
        const AVCodec* codec = avcodec_find_decoder(codecpar->codec_id);

        if (!codec) {
            std::cerr << "[FFmpegDecoder] Failed to find audio decoder: " << avcodec_get_name(codecpar->codec_id) << std::endl;
            return false;
        }

        m_audioCodecContext = avcodec_alloc_context3(codec);
        if (!m_audioCodecContext) {
            std::cerr << "[FFmpegDecoder] Failed to allocate audio codec context" << std::endl;
            return false;
        }

        int ret = avcodec_parameters_to_context(m_audioCodecContext, codecpar);
        if (ret < 0) {
            std::cerr << "[FFmpegDecoder] Failed to copy audio codec parameters to context" << std::endl;
            return false;
        }

        ret = avcodec_open2(m_audioCodecContext, codec, nullptr);
        if (ret < 0) {
            std::cerr << "[FFmpegDecoder] Failed to open audio codec" << std::endl;
            return false;
        }

        std::cout << "[FFmpegDecoder] Initialized audio decoder: " << avcodec_get_name(codecpar->codec_id) << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Failed to initialize audio decoder: " << e.what() << std::endl;
        return false;
    }
}

void FFmpegDecoder::cleanupDecoders() {
    if (m_swsContext) {
        sws_freeContext(m_swsContext);
        m_swsContext = nullptr;
    }

    if (m_swrContext) {
        swr_free(&m_swrContext);
        m_swrContext = nullptr;
    }

    if (m_aacSwrContext) {
        swr_free(&m_aacSwrContext);
        m_aacSwrContext = nullptr;
    }

    if (m_videoCodecContext) {
        avcodec_free_context(&m_videoCodecContext);
    }

    if (m_audioCodecContext) {
        avcodec_free_context(&m_audioCodecContext);
    }

    if (m_aacEncoderContext) {
        avcodec_free_context(&m_aacEncoderContext);
    }
}

void FFmpegDecoder::receiveLoop() {
    std::cout << "[FFmpegDecoder] Starting receive loop..." << std::endl;

    AVPacket* packet = av_packet_alloc();
    if (!packet) {
        std::cerr << "[FFmpegDecoder] Failed to allocate packet" << std::endl;
        return;
    }

    int errorCount = 0;
    const int maxErrors = 100;

    while (m_receiving) {
        int ret = av_read_frame(m_formatContext, packet);
        if (ret < 0) {
            errorCount++;
            if (ret == AVERROR_EOF) {
                std::cout << "[FFmpegDecoder] Stream ended" << std::endl;
                break;
            } else {
                char errbuf[AV_ERROR_MAX_STRING_SIZE];
                av_strerror(ret, errbuf, sizeof(errbuf));

                if (errorCount <= 5 || errorCount % 50 == 0) {
                    std::cerr << "[FFmpegDecoder] Failed to read frame (" << errorCount << "): " << errbuf << std::endl;
                }

                if (errorCount >= maxErrors) {
                    std::cerr << "[FFmpegDecoder] Too many errors, stopping receive loop" << std::endl;
                    break;
                }

                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                continue;
            }
        }

        errorCount = 0;
        if (packet->stream_index == m_videoStreamIndex) {
            if (m_outputCallback && packet->size > 0) {
                m_outputCallback(packet->data, packet->size, true);

                static int packetCount = 0;
                if (++packetCount % 100 == 0) {
                    std::cout << "[FFmpegDecoder] Sent " << packetCount << " H264 packets (size: " << packet->size << " bytes)" << std::endl;
                }
            }
        }
        else if (packet->stream_index == m_audioStreamIndex) {
            if (m_outputCallback && packet->size > 0) {
                m_outputCallback(packet->data, packet->size, false);

                static int packetCount = 0;
                if (++packetCount % 100 == 0) {
                    std::cout << "[FFmpegDecoder] Sent " << packetCount << " AAC packets (size: " << packet->size << " bytes)" << std::endl;
                }
            }
        }

        av_packet_unref(packet);
    }

    av_packet_free(&packet);
    std::cout << "[FFmpegDecoder] Receive loop ended" << std::endl;
}

void FFmpegDecoder::processVideoFrame(AVFrame* frame) {
    if (!m_outputCallback) return;

    try {
        size_t dataSize = frame->linesize[0] * frame->height;
        m_outputCallback(frame->data[0], dataSize, true);

        static int frameCount = 0;
        if (++frameCount % 100 == 0) {
            std::cout << "[FFmpegDecoder] Processed " << frameCount << " video frames" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Failed to process video frame: " << e.what() << std::endl;
    }
}

void FFmpegDecoder::processAudioFrame(AVFrame* frame) {
    if (!m_outputCallback) return;

    try {
        size_t dataSize = frame->nb_samples * frame->ch_layout.nb_channels * sizeof(int16_t);
        m_outputCallback(reinterpret_cast<uint8_t*>(frame->data[0]), dataSize, false);

    } catch (const std::exception& e) {
        std::cerr << "[FFmpegDecoder] Failed to process audio frame: " << e.what() << std::endl;
    }
}

void FFmpegDecoder::audioReceiveLoop(const std::string& audioUrl) {
    std::cout << "[FFmpegDecoder] Starting audio receive loop from: " << audioUrl << std::endl;

    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "[FFmpegDecoder] WSAStartup failed" << std::endl;
        return;
    }

    SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock == INVALID_SOCKET) {
        std::cerr << "[FFmpegDecoder] Failed to create audio socket" << std::endl;
        WSACleanup();
        return;
    }

    size_t portPos = audioUrl.find_last_of(':');
    int port = 1236;
    if (portPos != std::string::npos) {
        port = std::stoi(audioUrl.substr(portPos + 1));
    }

    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    addr.sin_addr.s_addr = INADDR_ANY;

    if (bind(sock, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
        std::cerr << "[FFmpegDecoder] Failed to bind audio socket to port " << port << std::endl;
        closesocket(sock);
        WSACleanup();
        return;
    }

    std::cout << "[FFmpegDecoder] Audio socket bound to port " << port << std::endl;

    u_long mode = 1;
    ioctlsocket(sock, FIONBIO, &mode);

    char buffer[4096];
    int packetCount = 0;

    while (m_audioReceiving) {
        int bytesReceived = recv(sock, buffer, sizeof(buffer), 0);

        if (bytesReceived > 0) {
            if (m_outputCallback) {
                m_outputCallback(reinterpret_cast<uint8_t*>(buffer), bytesReceived, false);
            }

            packetCount++;
            if (packetCount % 100 == 0) {
                std::cout << "[FFmpegDecoder] Received " << packetCount << " AAC audio packets (" << bytesReceived << " bytes)" << std::endl;
            }
        } else if (bytesReceived == SOCKET_ERROR) {
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK) {
                std::cerr << "[FFmpegDecoder] Audio socket error: " << error << std::endl;
                break;
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    closesocket(sock);
    WSACleanup();
    std::cout << "[FFmpegDecoder] Audio receive loop ended" << std::endl;
}

bool FFmpegDecoder::initializeAACEncoder() {
    std::cout << "[FFmpegDecoder] Initializing AAC encoder..." << std::endl;

    // For now, just return true to allow compilation and testing
    std::cout << "[FFmpegDecoder] AAC encoder initialization placeholder" << std::endl;
    return true;
}

void FFmpegDecoder::encodeAndSendAAC(const uint8_t* aacData, size_t dataSize) {
    if (m_outputCallback) {
        m_outputCallback(aacData, dataSize, false);

        static int aacPacketCount = 0;
        if (++aacPacketCount % 100 == 0) {
            std::cout << "[FFmpegDecoder] Forwarded " << aacPacketCount << " AAC packets (size: " << dataSize << " bytes)" << std::endl;
        }
    }
}

