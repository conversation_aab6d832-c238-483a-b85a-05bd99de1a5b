#pragma once

#include <functional>
#include <memory>
#include <thread>
#include <atomic>
#include <string>
#include <mutex>
#include <vector>

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libswscale/swscale.h>
#include <libswresample/swresample.h>
}


class FFmpegDecoder {
public:
    using OutputCallback = std::function<void(const uint8_t*, size_t, bool)>; // data, size, isVideo

    FFmpegDecoder();
    ~FFmpegDecoder();


    bool initialize();
    void cleanup();


    void setOutputCallback(OutputCallback callback) { m_outputCallback = callback; }


    bool startReceiving(const std::string& inputUrl = "udp://127.0.0.1:1235");
    bool startAudioReceiving(const std::string& audioUrl = "udp://127.0.0.1:1236");
    void stopReceiving();
    void stopAudioReceiving();

    void update();

    bool isReceiving() const { return m_receiving; }
    bool isAudioReceiving() const { return m_audioReceiving; }
    bool isInitialized() const { return m_initialized; }

private:
    void receiveLoop();
    void audioReceiveLoop(const std::string& audioUrl);
    bool openInputStream(const std::string& url);
    void closeInputStream();
    
    bool initializeVideoDecoder();
    bool initializeAudioDecoder();
    void cleanupDecoders();

    void processVideoFrame(AVFrame* frame);
    void processAudioFrame(AVFrame* frame);


    AVFormatContext* m_formatContext;
    AVCodecContext* m_videoCodecContext;
    AVCodecContext* m_audioCodecContext;
    
    int m_videoStreamIndex;
    int m_audioStreamIndex;
    

    SwsContext* m_swsContext;
    SwrContext* m_swrContext;
    

    AVFrame* m_videoFrame;
    AVFrame* m_audioFrame;
    uint8_t* m_videoBuffer;
    uint8_t* m_audioBuffer;
    

    std::thread m_receiveThread;
    std::thread m_audioReceiveThread;
    std::atomic<bool> m_receiving{false};
    std::atomic<bool> m_audioReceiving{false};
    std::atomic<bool> m_initialized{false};

    OutputCallback m_outputCallback;
    

    std::string m_inputUrl;
    int m_outputWidth{1920};
    int m_outputHeight{1080};
    int m_outputSampleRate{48000};
    int m_outputChannels{2};
};
