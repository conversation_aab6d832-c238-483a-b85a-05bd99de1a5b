#include "SignalingServer.h"
#include "WebRTCPeer.h"
#include "httplib.h"
#include "json.hpp"

#include <iostream>
#include <fstream>
#include <sstream>

using json = nlohmann::json;

SignalingServer::SignalingServer(int port) : m_port(port) {
    std::cout << "[SignalingServer] Initializing signaling server (port: " << port << ")" << std::endl;
}

SignalingServer::~SignalingServer() {
    stop();
}

bool SignalingServer::start() {
    if (m_running) {
        std::cout << "[SignalingServer] Server already running" << std::endl;
        return false;
    }

    try {
        m_running = true;
        m_serverThread = std::thread(&SignalingServer::runHttpServer, this);

        // Wait for server startup
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "[SignalingServer] Signaling server started successfully" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[SignalingServer] Failed to start: " << e.what() << std::endl;
        m_running = false;
        return false;
    }
}

void SignalingServer::stop() {
    if (!m_running) return;

    std::cout << "[SignalingServer] Stopping signaling server..." << std::endl;
    m_running = false;

    if (m_serverThread.joinable()) {
        m_serverThread.join();
    }

    // Clear peers
    std::lock_guard<std::mutex> lock(m_peersMutex);
    m_peers.clear();

    std::cout << "[SignalingServer] Singaling server stopped successfully" << std::endl;
}

void SignalingServer::runHttpServer() {
    httplib::Server server;

    // setup CORS
    server.set_pre_routing_handler([](const httplib::Request& req, httplib::Response& res) {
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        return httplib::Server::HandlerResponse::Unhandled;
    });

    // setup OPTIONS handler
    server.Options(".*", [](const httplib::Request&, httplib::Response& res) {
        return;
    });

    // offer static html
    server.Get("/", [this](const httplib::Request&, httplib::Response& res) {
        std::string content = getStaticFile("index.html");
        if (!content.empty()) {
            res.set_content(content, "text/html; charset=utf-8");
        } else {
            res.status = 404;
            res.set_content("Frontend not found", "text/plain");
        }
    });

    // WebRTC API
    server.Post("/api/offer", [this](const httplib::Request& req, httplib::Response& res) {
        try {
            json requestData = json::parse(req.body);
            std::string peerId = requestData["peerId"];
            std::string offer = requestData["offer"];

            std::cout << "[SignalingServer] Getoffer from " << peerId << std::endl;

            // create new WebRTC peer
            auto peer = std::make_shared<WebRTCPeer>(peerId);
            if (peer->handleOffer(offer)) {
                std::string answer = peer->getAnswer();
                
                // save peerconnection
                {
                    std::lock_guard<std::mutex> lock(m_peersMutex);
                    m_peers[peerId] = peer;
                }

                // inform
                if (m_onPeerConnected) {
                    m_onPeerConnected(peer);
                }

                json response;
                response["answer"] = answer;
                response["status"] = "success";
                res.set_content(response.dump(), "application/json");
                
                std::cout << "[SignalingServer] send answer to " << peerId << std::endl;
            } else {
                res.status = 500;
                res.set_content("{\"error\":\"Failed to process offer\"}", "application/json");
            }
        } catch (const std::exception& e) {
            std::cerr << "[SignalingServer] process answer failed: " << e.what() << std::endl;
            res.status = 500;
            res.set_content("{\"error\":\"Invalid request\"}", "application/json");
        }
    });

    // ICE canditate switch
    server.Post("/api/ice", [this](const httplib::Request& req, httplib::Response& res) {
        try {
            json requestData = json::parse(req.body);
            std::string peerId = requestData["peerId"];
            std::string candidate = requestData["candidate"];

            auto peer = getPeer(peerId);
            if (peer) {
                peer->addIceCandidate(candidate);
                res.set_content("{\"status\":\"success\"}", "application/json");
            } else {
                res.status = 404;
                res.set_content("{\"error\":\"Peer not found\"}", "application/json");
            }
        } catch (const std::exception& e) {
            res.status = 500;
            res.set_content("{\"error\":\"Invalid request\"}", "application/json");
        }
    });

    // get server status
    server.Get("/api/status", [this](const httplib::Request&, httplib::Response& res) {
        json status;
        status["connectedPeers"] = getConnectedPeerCount();
        status["serverRunning"] = m_running.load();
        res.set_content(status.dump(), "application/json");
    });

    std::cout << "[SignalingServer] HTTP server listening on port " << m_port << std::endl;
    
    // Start server
    server.listen("0.0.0.0", m_port);
}

std::shared_ptr<WebRTCPeer> SignalingServer::getPeer(const std::string& peerId) {
    std::lock_guard<std::mutex> lock(m_peersMutex);
    auto it = m_peers.find(peerId);
    return (it != m_peers.end()) ? it->second : nullptr;
}

size_t SignalingServer::getConnectedPeerCount() const {
    std::lock_guard<std::mutex> lock(m_peersMutex);
    return m_peers.size();
}

std::string SignalingServer::getStaticFile(const std::string& path) {

    if (path == "index.html") {
        return R"(<!DOCTYPE html>
<html>
<head>
    <title>WebRTC Stream</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>WebRTC Audio/Video Streaming</h1>
    <p>Please use Vue3 frontend client to connect</p>
    <p>Frontend URL: <a href="http://localhost:5173">http://localhost:5173</a></p>
</body>
</html>)";
    }
    return "";
}

std::string SignalingServer::getMimeType(const std::string& extension) {
    if (extension == ".html") return "text/html";
    if (extension == ".js") return "application/javascript";
    if (extension == ".css") return "text/css";
    if (extension == ".json") return "application/json";
    return "text/plain";
}
