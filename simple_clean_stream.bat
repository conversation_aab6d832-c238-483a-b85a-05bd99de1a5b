@echo off
echo ========================================
echo Simple Clean Audio/Video Stream Test
echo ========================================
echo.
echo This script streams a real video file with:
echo - Input: input.mp4 (place your video file here)
echo - Output: 640x480 at 15fps
echo - Audio: AAC stereo 128k
echo - Optimized for WebRTC transmission
echo.
echo Target: UDP port 1235
echo Press Ctrl+C to stop
echo ========================================
echo.

ffmpeg -re -i "input.mp4" -c:v libx264 -preset veryfast -tune zerolatency -profile:v baseline -level 3.0 -pix_fmt yuv420p -s 640x480 -r 15 -g 15 -keyint_min 15 -sc_threshold 0 -b:v 800k -maxrate 800k -bufsize 800k -x264-params "nal-hrd=cbr:force-cfr=1:bframes=0:ref=1" -c:a aac -b:a 128k -ar 44100 -ac 2 -f mpegts udp://127.0.0.1:1235

pause
