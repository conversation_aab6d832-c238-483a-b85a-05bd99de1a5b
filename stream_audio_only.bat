@echo off
echo ========================================
echo Audio Only Streaming Test
echo ========================================
echo.

REM 检查是否有视频文件
if exist "test.mp4" (
    set VIDEO_FILE=test.mp4
    echo 找到音频源文件: test.mp4
) else if exist "input.mp4" (
    set VIDEO_FILE=input.mp4
    echo 找到音频源文件: input.mp4
) else if exist "sample.mp4" (
    set VIDEO_FILE=sample.mp4
    echo 找到音频源文件: sample.mp4
) else (
    echo 使用合成音频源 (440Hz正弦波)
    set VIDEO_FILE=
)

echo.
echo 音频推流配置:
if defined VIDEO_FILE (
    echo - 输入文件: %VIDEO_FILE% (提取音频)
) else (
    echo - 输入源: 合成440Hz正弦波
)
echo - 音频格式: PCM 16bit (原始音频)
echo - 采样率: 44100Hz
echo - 声道: 立体声
echo - 目标: UDP://127.0.0.1:1236
echo.
echo 按任意键开始推流...
pause >nul

echo 开始推流 (仅音频)...

REM 使用简单的440Hz正弦波测试音频
echo 使用440Hz正弦波测试音频流程...
ffmpeg -f lavfi -i sine=frequency=440:sample_rate=48000:duration=0 -c:a pcm_s16le -ar 48000 -ac 2 -f s16le udp://127.0.0.1:1236

echo.
echo 音频推流结束
pause
