@echo off
echo ========================================
echo Real Video File Streaming Test
echo ========================================
echo.

REM 检查是否有视频文件
if exist "test.mp4" (
    set VIDEO_FILE=test.mp4
    echo 找到视频文件: test.mp4
) else if exist "input.mp4" (
    set VIDEO_FILE=input.mp4
    echo 找到视频文件: input.mp4
) else if exist "sample.mp4" (
    set VIDEO_FILE=sample.mp4
    echo 找到视频文件: sample.mp4
) else (
    echo 错误: 未找到视频文件!
    echo 请将视频文件重命名为以下之一:
    echo - test.mp4
    echo - input.mp4  
    echo - sample.mp4
    echo.
    echo 或者手动指定文件名:
    set /p VIDEO_FILE=请输入视频文件名: 
    if not exist "%VIDEO_FILE%" (
        echo 文件不存在: %VIDEO_FILE%
        pause
        exit /b 1
    )
)

echo.
echo 推流配置:
echo - 输入文件: %VIDEO_FILE%
echo - 输出分辨率: 640x480
echo - 帧率: 15fps
echo - 视频码率: 800kbps
echo - 音频码率: 128kbps (立体声)
echo - 目标: UDP://127.0.0.1:1235
echo.
echo 按任意键开始推流...
pause >nul

echo 开始推流...
ffmpeg -re -i "%VIDEO_FILE%" -c:v libx264 -preset veryfast -tune zerolatency -profile:v baseline -level 3.0 -pix_fmt yuv420p -s 640x480 -r 15 -g 15 -keyint_min 15 -sc_threshold 0 -b:v 800k -maxrate 800k -bufsize 800k -x264-params "nal-hrd=cbr:force-cfr=1:bframes=0:ref=1" -c:a aac -b:a 128k -ar 44100 -ac 2 -f mpegts udp://127.0.0.1:1235

echo.
echo 推流结束
pause
