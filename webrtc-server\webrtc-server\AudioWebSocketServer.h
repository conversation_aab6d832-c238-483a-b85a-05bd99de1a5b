#pragma once

#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>

class AudioWebSocketServer {
public:
    AudioWebSocketServer();
    ~AudioWebSocketServer();

    bool start(int port = 8081);
    void stop();
    void sendAudioData(const uint8_t* data, size_t size);

    bool isRunning() const { return m_running; }
    int getClientCount() const { return m_clientCount; }

private:
    void serverLoop();
    void handleClient(int clientSocket);
    void broadcastAudioData(const std::vector<uint8_t>& data);

    std::atomic<bool> m_running{false};
    std::atomic<int> m_clientCount{0};
    std::thread m_serverThread;
    int m_serverSocket{-1};
    int m_port{8081};

    std::mutex m_audioQueueMutex;
    std::queue<std::vector<uint8_t>> m_audioQueue;
    static const size_t MAX_QUEUE_SIZE = 100;

    std::mutex m_clientsMutex;
    std::vector<int> m_clients;
};
