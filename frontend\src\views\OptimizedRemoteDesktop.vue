<template>
  <div class="remote-desktop">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="connection-info">
        <el-tag 
          :type="connectionTagType" 
          size="large"
          class="status-tag"
        >
          {{ store.connectionStatus }}
        </el-tag>
        
        <span v-if="store.isReady" class="quality-indicator">
          质量: {{ connectionQualityText }}
        </span>
      </div>

      <div class="controls">
        <el-button 
          v-if="!store.connected && !store.connecting"
          type="primary" 
          @click="connect"
          :loading="store.connecting"
        >
          连接
        </el-button>
        
        <el-button 
          v-if="store.connected"
          type="danger" 
          @click="disconnect"
        >
          断开
        </el-button>

        <el-switch
          v-model="store.inputEnabled"
          @change="toggleInput"
          active-text="输入控制"
          inactive-text="输入禁用"
          class="input-switch"
        />
      </div>
    </div>

    <!-- 视频显示区域 -->
    <div class="video-container" ref="videoContainer">
      <canvas 
        ref="videoCanvas"
        class="video-canvas"
        @contextmenu.prevent
      />
      
      <!-- 连接状态覆盖层 -->
      <div v-if="!store.isReady" class="overlay">
        <div class="overlay-content">
          <el-icon v-if="store.connecting" class="loading-icon">
            <Loading />
          </el-icon>
          <h3>{{ overlayMessage }}</h3>
          <p v-if="store.error" class="error-message">{{ store.error }}</p>
        </div>
      </div>

      <!-- 视频统计信息 -->
      <div v-if="store.videoReceived && showStats" class="stats-overlay">
        <div class="stats-item">分辨率: {{ store.videoStats.resolution }}</div>
        <div class="stats-item">帧率: {{ store.videoStats.fps }} FPS</div>
        <div class="stats-item">总帧数: {{ store.videoStats.totalFrames }}</div>
        <div class="stats-item">丢帧: {{ store.videoStats.droppedFrames }}</div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span v-if="store.videoReceived">
          视频: {{ store.videoStats.resolution }} @ {{ store.videoStats.fps }}fps
        </span>
        <span v-else>等待视频流...</span>
      </div>

      <div class="status-right">
        <el-button 
          text 
          @click="showStats = !showStats"
          :type="showStats ? 'primary' : 'info'"
        >
          统计信息
        </el-button>
        
        <el-button 
          text 
          @click="showLogs = !showLogs"
          :type="showLogs ? 'primary' : 'info'"
        >
          日志 ({{ store.logs.length }})
        </el-button>
      </div>
    </div>

    <!-- 日志面板 -->
    <el-drawer
      v-model="showLogs"
      title="连接日志"
      direction="rtl"
      size="400px"
    >
      <div class="logs-container">
        <el-button @click="store.clearLogs()" size="small" class="clear-logs">
          清空日志
        </el-button>
        
        <div class="log-list">
          <div 
            v-for="log in store.logs" 
            :key="log.time"
            :class="['log-item', `log-${log.type}`]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useWebRTCStore } from '@/stores/webrtc'
import { initWebRTCConnection, disconnectWebRTC, sendData, getConnectionState } from '@/composables/useWebRTCConnection'
import { initH264Player, handleH264Data, getPlayerState, resetPlayer, cleanupPlayer } from '@/composables/useOptimizedH264Player'
import { initInputCapture, setInputEnabled, cleanupInputCapture } from '@/composables/useInputCapture'
import { initAudioPlayer, setupAudioDataChannel, getAudioState } from '@/composables/useSimpleAudioPlayer'

// Store
const store = useWebRTCStore()

// 组件状态
const showStats = ref(false)
const showLogs = ref(false)
const audioState = ref(getAudioState())

// DOM引用
const videoContainer = ref(null)
const videoCanvas = ref(null)

// 音频播放器
let audioContext = null
let audioDecoder = null
let audioQueue = []

// AAC解码器
let aacDecoder = null
let audioStartTime = null
let nextPlayTime = 0
let audioFrameCount = 0

// 计算属性
const connectionTagType = computed(() => {
  if (store.connected) return 'success'
  if (store.connecting) return 'warning'
  if (store.error) return 'danger'
  return 'info'
})

const connectionQualityText = computed(() => {
  const quality = store.connectionQuality
  const qualityMap = {
    'good': '优秀',
    'fair': '良好', 
    'poor': '较差',
    'disconnected': '断开'
  }
  return qualityMap[quality] || '未知'
})

const overlayMessage = computed(() => {
  if (store.connecting) return '正在连接...'
  if (store.error) return '连接失败'
  if (!store.connected) return '点击连接按钮开始'
  if (!store.dataChannelOpen) return '等待数据通道...'
  if (!store.videoReceived) return '等待视频流...'
  return '连接就绪'
})

// 连接方法
async function connect() {
  try {
    store.addLog('info', '开始连接到服务器...')

    // 初始化音频播放器
    await initAudioPlayer()

    // 同时初始化组件内部的音频上下文
    if (!audioContext) {
      audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 48000, // 匹配后端的48kHz采样率
        latencyHint: 'interactive'
      })

      // 确保音频上下文启动
      if (audioContext.state === 'suspended') {
        await audioContext.resume()
      }

      console.log(`✅ 组件音频上下文初始化成功 - 采样率: ${audioContext.sampleRate}Hz, 状态: ${audioContext.state}`)

      // 重置音频播放状态
      audioStartTime = null
      nextPlayTime = 0
      audioFrameCount = 0

      // 初始化AAC解码器
      try {
        await initAACDecoder()
        console.log('✅ AAC解码器初始化完成')
      } catch (error) {
        console.error('❌ AAC解码器初始化失败:', error)
        throw error
      }
    }

    await initWebRTCConnection(store.serverUrl, {
      onDataChannelMessage: handleDataChannelMessage,
      onConnectionStateChange: handleConnectionStateChange,
      onError: handleConnectionError
    })

    // 音频将通过WebRTC DataChannel传输
    store.addLog('info', '音频将通过WebRTC DataChannel传输')

  } catch (error) {
    ElMessage.error(`连接失败: ${error.message}`)
    store.addLog('error', `连接失败: ${error.message}`)
  }
}

// 断开连接
function disconnect() {
  disconnectWebRTC()
  resetPlayer()

  // 清理音频上下文
  if (audioContext) {
    audioContext.close().catch(err => {
      console.warn('🔊 关闭音频上下文失败:', err)
    })
    audioContext = null
  }

  // 清理音频解码器
  if (audioDecoder) {
    audioDecoder.close()
    audioDecoder = null
  }

  store.resetStats()
  store.addLog('info', '已断开连接')
  ElMessage.info('已断开连接')
}

// 切换输入控制
function toggleInput(enabled) {
  setInputEnabled(enabled)
  store.addLog('info', `输入控制${enabled ? '已启用' : '已禁用'}`)
}

// WebRTC事件处理
// 分片重组缓存
const fragmentCache = new Map()

// 定期清理过期的分片缓存
setInterval(() => {
  const now = Date.now()
  for (const [frameId, frameData] of fragmentCache.entries()) {
    if (now - frameData.timestamp > 5000) { // 5秒超时
      fragmentCache.delete(frameId)
      console.warn('清理过期分片:', frameId)
    }
  }
}, 1000)

// 处理来自DataChannel的音频数据 (AAC格式)
function handleAudioDataFromDataChannel(arrayBuffer) {
  try {
    // 跳过第一个字节（协议标识0x02）
    const audioData = arrayBuffer.slice(1)

    audioFrameCount++

    // 检查数据有效性
    if (audioData.byteLength < 10) {
      return // 数据太小，跳过
    }

    // 确保音频上下文已初始化
    if (!audioContext) {
      console.warn('🔊 音频上下文未初始化，尝试创建...')
      audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 48000,
        latencyHint: 'interactive'
      })

      // 确保音频上下文启动
      if (audioContext.state === 'suspended') {
        audioContext.resume().catch(err => {
          console.error('🔊 启动音频上下文失败:', err)
        })
      }
    }

    // 检查AAC解码器状态
    if (!aacDecoder) {
      console.error('🔊 AAC解码器未初始化，请先连接')
      return
    }

    // 每100帧显示一次调试信息
    if (audioFrameCount % 100 === 0) {
      console.log(`🔊 AAC音频帧 #${audioFrameCount}: ${audioData.byteLength} 字节`)

      // 显示前几个字节（AAC ADTS头部）
      const firstBytes = new Uint8Array(audioData.slice(0, 16))
      console.log(`🔊 AAC数据: [${Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join(' ')}]`)

      // 检查ADTS同步字
      if (firstBytes[0] === 0xFF && (firstBytes[1] & 0xF0) === 0xF0) {
        console.log(`✅ 检测到ADTS头部`)
      } else {
        console.log(`⚠️ 未检测到ADTS头部`)
      }
    }

    // 解码AAC数据
    if (aacDecoder && aacDecoder.state === 'configured') {
      const chunk = new EncodedAudioChunk({
        type: 'key',
        timestamp: audioFrameCount * 1024 * 1000000 / 48000, // 微秒
        data: audioData
      })

      aacDecoder.decode(chunk)
    } else {
      console.warn('🔊 AAC解码器未就绪')
    }

  } catch (error) {
    console.error('🔊 处理AAC音频数据失败:', error)
  }
}

// 播放测试音调
function playTestTone() {
  if (!audioContext) return

  try {
    const duration = 0.2 // 200ms
    const frequency = 440 // 440Hz A音
    const sampleRate = audioContext.sampleRate
    const frameCount = Math.floor(duration * sampleRate)

    const buffer = audioContext.createBuffer(2, frameCount, sampleRate)
    const leftChannel = buffer.getChannelData(0)
    const rightChannel = buffer.getChannelData(1)

    // 生成440Hz正弦波
    for (let i = 0; i < frameCount; i++) {
      const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3 // 30%音量
      leftChannel[i] = sample
      rightChannel[i] = sample
    }

    const source = audioContext.createBufferSource()
    source.buffer = buffer
    source.connect(audioContext.destination)
    source.start()

    console.log(`🔊 测试音调播放: ${frequency}Hz, ${duration}s, ${frameCount}样本`)
  } catch (error) {
    console.error('🔊 测试音调播放失败:', error)
  }
}



// 初始化AAC解码器
async function initAACDecoder() {
  try {
    if (!window.AudioDecoder) {
      console.error('🔊 浏览器不支持AudioDecoder API')
      throw new Error('AudioDecoder not supported')
    }

    console.log('🔊 开始初始化AAC解码器...')

    // 测试多种AAC配置
    const configs = [
      { codec: 'mp4a.40.2', sampleRate: 48000, numberOfChannels: 2, description: 'AAC-LC 48kHz Stereo' },
      { codec: 'mp4a.40.5', sampleRate: 48000, numberOfChannels: 2, description: 'HE-AAC 48kHz Stereo' },
      { codec: 'mp4a.40.2', sampleRate: 44100, numberOfChannels: 2, description: 'AAC-LC 44.1kHz Stereo' },
      { codec: 'mp4a.40.2', sampleRate: 48000, numberOfChannels: 1, description: 'AAC-LC 48kHz Mono' }
    ]

    let selectedConfig = null

    for (const config of configs) {
      try {
        console.log(`🔊 测试配置: ${config.description}`)
        const result = await AudioDecoder.isConfigSupported(config)
        console.log(`🔊 配置支持结果:`, result)

        if (result.supported) {
          selectedConfig = config
          console.log(`✅ 选择配置: ${config.description}`)
          break
        }
      } catch (error) {
        console.log(`❌ 配置测试失败: ${config.description}`, error)
      }
    }

    if (!selectedConfig) {
      throw new Error('没有找到支持的AAC配置')
    }

    // 创建解码器
    aacDecoder = new AudioDecoder({
      output: handleDecodedAudio,
      error: (error) => {
        console.error('🔊 AAC解码错误:', error)
        // 不要重置解码器，继续尝试
      }
    })

    // 配置解码器
    aacDecoder.configure(selectedConfig)
    console.log('✅ AAC解码器配置成功:', selectedConfig)

    // 等待解码器就绪
    await new Promise((resolve, reject) => {
      const checkReady = () => {
        if (aacDecoder.state === 'configured') {
          resolve()
        } else if (aacDecoder.state === 'closed') {
          reject(new Error('解码器已关闭'))
        } else {
          setTimeout(checkReady, 10)
        }
      }
      checkReady()
    })

    console.log('✅ AAC解码器就绪，状态:', aacDecoder.state)

  } catch (error) {
    console.error('🔊 AAC解码器初始化失败:', error)
    throw error
  }
}

// 处理解码后的音频数据
function handleDecodedAudio(audioData) {
  try {
    if (!audioContext) return

    // 计算正确的播放时间
    if (audioStartTime === null) {
      audioStartTime = audioContext.currentTime
      nextPlayTime = audioStartTime
      console.log(`🔊 AAC音频播放开始时间: ${audioStartTime.toFixed(3)}s`)
    }

    // 创建AudioBuffer
    const audioBuffer = audioContext.createBuffer(
      audioData.numberOfChannels,
      audioData.numberOfFrames,
      audioData.sampleRate
    )

    // 复制音频数据
    for (let channel = 0; channel < audioData.numberOfChannels; channel++) {
      const channelData = audioBuffer.getChannelData(channel)
      audioData.copyTo(channelData, { planeIndex: channel })
    }

    // 调试信息
    if (audioFrameCount % 100 === 0) {
      console.log(`🔊 解码音频: ${audioData.numberOfChannels}声道, ${audioData.numberOfFrames}帧, ${audioData.sampleRate}Hz`)

      // 分析音频内容
      const leftChannel = audioBuffer.getChannelData(0)
      let minSample = 1, maxSample = -1
      for (let i = 0; i < Math.min(100, leftChannel.length); i++) {
        minSample = Math.min(minSample, leftChannel[i])
        maxSample = Math.max(maxSample, leftChannel[i])
      }
      console.log(`🔊 解码样本范围: [${minSample.toFixed(3)}, ${maxSample.toFixed(3)}]`)
    }

    // 使用调度播放
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)

    const playTime = Math.max(nextPlayTime, audioContext.currentTime + 0.01)
    source.start(playTime)

    // 更新下一帧的播放时间
    const frameDuration = audioData.numberOfFrames / audioData.sampleRate
    nextPlayTime = playTime + frameDuration

    if (audioFrameCount % 100 === 0) {
      console.log(`🔊 AAC播放调度: 播放时间=${playTime.toFixed(3)}s, 帧长度=${frameDuration.toFixed(3)}s`)
    }

    // 添加测试音调
    if (audioFrameCount % 1000 === 0) {
      console.log(`🔊 播放测试音调 (440Hz正弦波)`)
      playTestTone()
    }

  } catch (error) {
    console.error('🔊 处理解码音频失败:', error)
  }
}

function handleDataChannelMessage(event) {
  try {
    const data = new Uint8Array(event.data)
    // console.log('🔥 收到DataChannel消息:', data.length, '字节, 类型:', data[0])  // 减少日志输出

    // 添加调试信息
    console.log('收到DataChannel消息:', data.length, '字节, 类型:', data[0])

    // 检查数据类型标识
    if (data.length > 0) {
      const dataType = data[0]

      if (dataType === 0x01) {
        // 完整视频数据
        const payload = data.slice(1)
        if (handleH264Data(payload)) {
          if (!store.videoReceived) {
            store.setVideoReceived(true)
            store.addLog('success', '开始接收视频流')
          }

          // 更新视频统计
          const playerState = getPlayerState()
          store.updateVideoStats(playerState.stats)
        }
      } else if (dataType === 0x02) {
        // 音频数据 - 通过简化音频播放器处理
        console.log('接收到音频数据:', data.slice(1).length, '字节')
        // 注意：这里直接传递完整的data（包含类型标识），让音频播放器处理
        handleAudioDataFromDataChannel(event.data)
      } else if (dataType === 0x03) {
        // 分片视频数据
        if (data.length >= 9) { // 至少包含头部信息
          const frameId = (data[1] << 24) | (data[2] << 16) | (data[3] << 8) | data[4]
          const chunkIndex = (data[5] << 8) | data[6]
          const totalChunks = (data[7] << 8) | data[8]
          const payload = data.slice(9)

          // 获取或创建帧缓存
          if (!fragmentCache.has(frameId)) {
            fragmentCache.set(frameId, {
              chunks: new Array(totalChunks),
              receivedCount: 0,
              totalChunks: totalChunks,
              timestamp: Date.now()
            })
          }

          const frameData = fragmentCache.get(frameId)

          // 存储分片
          if (!frameData.chunks[chunkIndex]) {
            frameData.chunks[chunkIndex] = payload
            frameData.receivedCount++
          }

          // 检查是否收集完所有分片
          if (frameData.receivedCount === frameData.totalChunks) {
            // 重组完整帧
            const totalSize = frameData.chunks.reduce((sum, chunk) => sum + chunk.length, 0)
            const completeFrame = new Uint8Array(totalSize)
            let offset = 0

            for (const chunk of frameData.chunks) {
              completeFrame.set(chunk, offset)
              offset += chunk.length
            }

            // 处理完整帧
            if (handleH264Data(completeFrame)) {
              if (!store.videoReceived) {
                store.setVideoReceived(true)
                store.addLog('success', '开始接收视频流')
              }

              // 更新视频统计
              const playerState = getPlayerState()
              store.updateVideoStats(playerState.stats)
            }

            // 清理缓存
            fragmentCache.delete(frameId)
          }
        }
      } else {
        // 可能是输入事件或其他数据，按原来的方式处理
        if (handleH264Data(data)) {
          if (!store.videoReceived) {
            store.setVideoReceived(true)
            store.addLog('success', '开始接收视频流')
          }

          // 更新视频统计
          const playerState = getPlayerState()
          store.updateVideoStats(playerState.stats)
        }
      }
    }

  } catch (error) {
    console.error('处理数据失败:', error)
    store.addLog('error', `数据处理失败: ${error.message}`)
  }
}

// 音频数据处理
let audioTimestamp = 0
const AUDIO_FRAME_DURATION = 23219 // AAC帧持续时间 (微秒)

async function handleAudioData(audioData) {
  try {
    if (!audioContext) {
      audioContext = new AudioContext()
      // 确保音频上下文已启动
      if (audioContext.state === 'suspended') {
        await audioContext.resume()
      }
    }

    if (!audioDecoder) {
      audioDecoder = new AudioDecoder({
        output: (decodedAudio) => {
          try {
            // 创建音频缓冲区
            const audioBuffer = audioContext.createBuffer(
              decodedAudio.numberOfChannels,
              decodedAudio.numberOfFrames,
              decodedAudio.sampleRate
            )

            // 复制音频数据
            for (let channel = 0; channel < decodedAudio.numberOfChannels; channel++) {
              const channelData = new Float32Array(decodedAudio.numberOfFrames)
              decodedAudio.copyTo(channelData, { planeIndex: channel })
              audioBuffer.copyToChannel(channelData, channel)
            }

            // 播放音频
            const source = audioContext.createBufferSource()
            source.buffer = audioBuffer
            source.connect(audioContext.destination)

            // 计算播放时间以保持同步
            const playTime = audioContext.currentTime + 0.1
            source.start(playTime)

            // console.log('🔊 播放音频帧:', decodedAudio.numberOfFrames, '采样') // 减少日志

          } catch (error) {
            console.error('音频播放失败:', error)
          }
        },
        error: (error) => {
          console.error('音频解码错误:', error)
          // 重置解码器
          audioDecoder = null
        }
      })

      // 配置AAC解码器 - 简化配置
      try {
        const config = {
          codec: 'mp4a.40.2', // AAC-LC
          sampleRate: 44100,
          numberOfChannels: 2 // 立体声
        }

        // 检查浏览器支持
        if (AudioDecoder.isConfigSupported) {
          const support = await AudioDecoder.isConfigSupported(config)
          if (!support.supported) {
            console.error('音频配置不支持:', config)
            return
          }
        }

        audioDecoder.configure(config)
        console.log('✅ 音频解码器配置成功')
      } catch (error) {
        console.error('音频解码器配置失败:', error)
        return
      }
    }

    // 检查数据有效性
    if (audioData.length < 7) { // AAC最小帧大小
      return
    }

    // 创建音频块并解码
    const chunk = new EncodedAudioChunk({
      type: 'key', // AAC帧都是关键帧
      timestamp: audioTimestamp,
      duration: AUDIO_FRAME_DURATION,
      data: audioData.buffer.slice(audioData.byteOffset, audioData.byteOffset + audioData.byteLength)
    })

    // 更新时间戳
    audioTimestamp += AUDIO_FRAME_DURATION

    if (audioDecoder.state === 'configured') {
      audioDecoder.decode(chunk)
    } else {
      console.warn('音频解码器未就绪，状态:', audioDecoder.state)
    }

  } catch (error) {
    console.error('音频处理失败:', error)
  }
}

function handleConnectionStateChange(state) {
  const connectionState = getConnectionState()
  store.updateConnectionState(connectionState)
  
  if (state === 'connected') {
    store.addLog('success', 'WebRTC连接已建立')
    ElMessage.success('连接成功')
  } else if (state === 'disconnected' || state === 'failed') {
    store.addLog('warning', 'WebRTC连接已断开')
    store.setVideoReceived(false)
  }
}

function handleConnectionError(error) {
  store.addLog('error', `连接错误: ${error.message}`)
  ElMessage.error(`连接错误: ${error.message}`)
}

// 发送输入事件
function sendInputEvent(eventData) {
  try {
    const connectionState = getConnectionState()
    if (!connectionState.connected || !connectionState.dataChannelOpen) {
      console.warn('连接未就绪，跳过输入事件')
      return
    }

    sendData(eventData)
    console.log('✅ 发送输入事件:', eventData.type)
  } catch (error) {
    console.warn('发送输入事件失败:', error.message)
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()
  
  // 初始化H264播放器
  if (videoCanvas.value) {
    const success = await initH264Player(videoCanvas.value)
    if (success) {
      store.addLog('info', 'H264播放器初始化成功')
    } else {
      store.addLog('error', 'H264播放器初始化失败')
    }
  }
  
  // 初始化输入捕获
  if (videoContainer.value) {
    initInputCapture(videoContainer.value, sendInputEvent)
    store.addLog('info', '输入捕获系统已初始化')
  }
})

onUnmounted(() => {
  disconnectWebRTC()
  cleanupPlayer()
  cleanupInputCapture()

  // 清理音频资源
  if (audioContext) {
    audioContext.close().catch(err => {
      console.warn('🔊 关闭音频上下文失败:', err)
    })
    audioContext = null
  }

  if (audioDecoder) {
    audioDecoder.close()
    audioDecoder = null
  }
})
</script>

<style scoped>
.remote-desktop {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.connection-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  font-weight: 600;
}

.quality-indicator {
  color: #606266;
  font-size: 14px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-switch {
  margin-left: 8px;
}

.video-container {
  flex: 1;
  position: relative;
  background: #000;
  overflow: hidden;
}

.video-canvas {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: none;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.overlay-content {
  text-align: center;
}

.loading-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  color: #f56c6c;
  margin-top: 8px;
}

.stats-overlay {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 12px;
  border-radius: 6px;
  font-family: monospace;
  font-size: 12px;
}

.stats-item {
  margin-bottom: 4px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: white;
  border-top: 1px solid #e4e7ed;
  font-size: 14px;
  color: #606266;
}

.status-right {
  display: flex;
  gap: 8px;
}

.logs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.clear-logs {
  margin-bottom: 16px;
}

.log-list {
  flex: 1;
  overflow-y: auto;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 8px;
}

.log-message {
  color: #303133;
}

.log-info {
  border-left: 3px solid #409eff;
}

.log-success {
  border-left: 3px solid #67c23a;
}

.log-warning {
  border-left: 3px solid #e6a23c;
}

.log-error {
  border-left: 3px solid #f56c6c;
}
</style>
