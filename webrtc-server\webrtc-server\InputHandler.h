#pragma once

#include <string>
#include <atomic>
#include <windows.h>

class InputHandler {
public:
    InputHandler();
    ~InputHandler();

    bool initialize();
    void cleanup();
    void update();


    void processEvent(const std::string& eventData);

  
    bool isInitialized() const { return m_initialized; }

private:
    void handleMouseEvent(const std::string& eventData);
    void handleKeyboardEvent(const std::string& eventData);
    void handleWheelEvent(const std::string& eventData);

    std::atomic<bool> m_initialized{false};
};
