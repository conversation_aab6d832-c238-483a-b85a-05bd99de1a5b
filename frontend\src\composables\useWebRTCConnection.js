/**
 * 🔗 WebRTC连接管理器
 * 专门适配libdatachannel后端的简化连接逻辑
 */

import { ref, reactive } from 'vue'

// 连接状态
const connectionState = reactive({
  connected: false,
  connecting: false,
  status: '未连接',
  dataChannelOpen: false,
  error: null
})

// WebRTC对象
let peerConnection = null
let dataChannel = null
let videoDataChannel = null
let audioDataChannel = null
let serverUrl = ''
let currentPeerId = ''

// 事件回调
const callbacks = {
  onDataChannelMessage: null,
  onConnectionStateChange: null,
  onError: null
}

/**
 * 初始化WebRTC连接
 * @param {string} url - 服务器地址
 * @param {Object} eventCallbacks - 事件回调函数
 */
export async function initWebRTCConnection(url, eventCallbacks = {}) {
  if (connectionState.connecting || connectionState.connected) {
    throw new Error('连接已存在或正在连接中')
  }

  serverUrl = url
  // 设置回调
  Object.assign(callbacks, eventCallbacks)

  connectionState.connecting = true
  connectionState.status = '连接中...'
  connectionState.error = null

  try {
    // 创建PeerConnection
    peerConnection = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    })

    // 设置事件监听器
    setupPeerConnectionEvents()

    // 监听来自服务器的DataChannel（服务器会创建DataChannel）
    peerConnection.ondatachannel = (event) => {
      console.log('🔥 接收到DataChannel:', event.channel.label, '状态:', event.channel.readyState)

      // 根据channel label选择合适的处理
      if (event.channel.label === 'input') {
        dataChannel = event.channel
        setupDataChannelEvents(dataChannel, 'input')
        console.log('✅ 输入DataChannel已设置')
        checkAllChannelsReady()
      } else if (event.channel.label === 'video') {
        videoDataChannel = event.channel
        setupDataChannelEvents(videoDataChannel, 'video')
        console.log('✅ 视频DataChannel已设置')
        checkAllChannelsReady()
      } else if (event.channel.label === 'audio') {
        audioDataChannel = event.channel
        setupDataChannelEvents(audioDataChannel, 'audio')
        console.log('✅ 音频DataChannel已设置')
        checkAllChannelsReady()
      } else {
        console.log('❓ 未知DataChannel:', event.channel.label)
      }
    }

    // 生成唯一的peerId
    currentPeerId = 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    console.log('客户端ID:', currentPeerId)

    // 添加媒体轨道和DataChannel以匹配服务器顺序
    console.log('添加媒体轨道和DataChannel以匹配服务器顺序...')

    // 1. 按照服务器的顺序添加轨道：video first, then audio
    peerConnection.addTransceiver('video', { direction: 'recvonly' })
    peerConnection.addTransceiver('audio', { direction: 'recvonly' })

    // 2. 创建客户端DataChannel以确保offer中包含DataChannel m-line
    const clientDataChannel = peerConnection.createDataChannel('client-input', {
      ordered: true
    })

    // 设置客户端DataChannel事件
    clientDataChannel.onopen = () => {
      console.log('客户端DataChannel已打开')
      dataChannel = clientDataChannel
      setupDataChannelEvents(dataChannel, 'client-input')
    }

    console.log('客户端DataChannel已创建')

    // 创建offer
    console.log('创建客户端offer...')
    const offer = await peerConnection.createOffer({
      offerToReceiveVideo: true,
      offerToReceiveAudio: true
    })
    await peerConnection.setLocalDescription(offer)

    // 等待ICE收集完成
    console.log('等待ICE候选者收集...')
    await waitForIceGathering(peerConnection)

    // 获取完整的SDP（包含ICE候选者）
    const completeOffer = peerConnection.localDescription
    console.log('完整offer SDP长度:', completeOffer.sdp.length)

    // 调试: 打印offer中的m-line顺序
    const offerLines = completeOffer.sdp.split('\n')
    const mLines = offerLines.filter(line => line.startsWith('m='))
    console.log('Offer中的m-lines:', mLines)

    // 发送offer到服务器
    console.log('发送offer到服务器...')
    const offerResponse = await fetch(`${serverUrl}/api/offer`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        peerId: currentPeerId,
        offer: completeOffer.sdp
      })
    })

    if (!offerResponse.ok) {
      throw new Error(`HTTP ${offerResponse.status}: ${offerResponse.statusText}`)
    }

    const responseData = await offerResponse.json()
    console.log('收到服务器answer')

    // 调试: 打印answer中的m-line顺序
    const answerLines = responseData.answer.split('\n')
    const answerMLines = answerLines.filter(line => line.startsWith('m='))
    console.log('Answer中的m-lines:', answerMLines)

    // 设置远程描述
    await peerConnection.setRemoteDescription({
      type: 'answer',
      sdp: responseData.answer
    })

    console.log('WebRTC握手完成，等待连接建立...')

  } catch (error) {
    connectionState.connecting = false
    connectionState.status = '连接失败'
    connectionState.error = error.message
    
    if (peerConnection) {
      peerConnection.close()
      peerConnection = null
    }
    
    if (callbacks.onError) {
      callbacks.onError(error)
    }
    
    throw error
  }
}

/**
 * 断开WebRTC连接
 */
export function disconnectWebRTC() {
  if (peerConnection) {
    peerConnection.close()
    peerConnection = null
  }

  if (dataChannel) {
    dataChannel.close()
    dataChannel = null
  }

  if (videoDataChannel) {
    videoDataChannel.close()
    videoDataChannel = null
  }

  if (audioDataChannel) {
    audioDataChannel.close()
    audioDataChannel = null
  }

  connectionState.connected = false
  connectionState.connecting = false
  connectionState.dataChannelOpen = false
  connectionState.status = '未连接'
  connectionState.error = null
  currentPeerId = ''

  if (callbacks.onConnectionStateChange) {
    callbacks.onConnectionStateChange('disconnected')
  }
}

/**
 * 发送数据到服务器
 * @param {Object|string} data - 要发送的数据
 */
export function sendData(data) {
  if (!dataChannel || dataChannel.readyState !== 'open') {
    throw new Error('DataChannel未就绪')
  }

  const message = typeof data === 'string' ? data : JSON.stringify(data)
  dataChannel.send(message)
}

/**
 * 获取连接状态
 */
export function getConnectionState() {
  return connectionState
}

/**
 * 设置PeerConnection事件监听器
 */
function setupPeerConnectionEvents() {
  // 连接状态变化
  peerConnection.onconnectionstatechange = () => {
    const state = peerConnection.connectionState
    console.log('PeerConnection状态:', state)

    if (state === 'connected') {
      connectionState.connected = true
      connectionState.connecting = false
      connectionState.status = '已连接'
    } else if (state === 'disconnected' || state === 'failed') {
      connectionState.connected = false
      connectionState.connecting = false
      connectionState.status = '连接断开'
    } else if (state === 'connecting') {
      connectionState.status = '正在连接...'
    }

    if (callbacks.onConnectionStateChange) {
      callbacks.onConnectionStateChange(state)
    }
  }

  // ICE连接状态变化
  peerConnection.oniceconnectionstatechange = () => {
    const iceState = peerConnection.iceConnectionState
    console.log('ICE连接状态:', iceState)

    if (iceState === 'connected' || iceState === 'completed') {
      console.log('ICE连接已建立，等待DataChannel打开...')
    } else if (iceState === 'failed') {
      console.error('ICE连接失败')
      if (callbacks.onError) {
        callbacks.onError(new Error('ICE连接失败'))
      }
    }
  }

  // ICE候选者 - 暂时不单独发送，等待收集完成后一次性发送
  peerConnection.onicecandidate = (event) => {
    if (event.candidate) {
      console.log('收集到ICE候选者:', event.candidate.candidate.substring(0, 50) + '...')
    } else {
      console.log('ICE候选者收集完成')
    }
  }
}

/**
 * 等待ICE候选者收集完成
 * @param {RTCPeerConnection} pc - PeerConnection对象
 */
function waitForIceGathering(pc) {
  return new Promise((resolve) => {
    if (pc.iceGatheringState === 'complete') {
      resolve()
      return
    }

    const timeout = setTimeout(() => {
      console.warn('ICE收集超时，继续发送offer')
      resolve()
    }, 3000) // 3秒超时

    pc.addEventListener('icegatheringstatechange', () => {
      if (pc.iceGatheringState === 'complete') {
        clearTimeout(timeout)
        console.log('ICE候选者收集完成')
        resolve()
      }
    })
  })
}

/**
 * 检查所有DataChannel是否就绪
 */
function checkAllChannelsReady() {
  const hasInput = dataChannel && dataChannel.readyState === 'open'
  const hasVideo = videoDataChannel && videoDataChannel.readyState === 'open'
  const hasAudio = audioDataChannel && audioDataChannel.readyState === 'open'

  if (hasInput && hasVideo && hasAudio) {
    connectionState.dataChannelOpen = true
    console.log('🎉 所有DataChannel已就绪!')

    if (callbacks.onConnectionStateChange) {
      callbacks.onConnectionStateChange('ready')
    }
  }
}

/**
 * 设置DataChannel事件监听器
 */
function setupDataChannelEvents(channel, label) {
  channel.onopen = () => {
    console.log(`🔥 ${label} DataChannel已打开`)
    if (label === 'input' || label === 'client-input') {
      connectionState.dataChannelOpen = true
    }
    // 检查所有通道是否就绪
    checkAllChannelsReady()
  }

  channel.onclose = () => {
    console.log(`${label} DataChannel已关闭`)
    if (label === 'input' || label === 'client-input') {
      connectionState.dataChannelOpen = false
    }
  }

  channel.onerror = (error) => {
    console.error(`${label} DataChannel错误:`, error)
    if (callbacks.onError) {
      callbacks.onError(error)
    }
  }

  channel.onmessage = (event) => {
    // console.log(`🔥 ${label} DataChannel收到消息:`, event.data.byteLength, '字节')  // 减少日志输出
    if (callbacks.onDataChannelMessage) {
      callbacks.onDataChannelMessage(event)
    }
  }
}
