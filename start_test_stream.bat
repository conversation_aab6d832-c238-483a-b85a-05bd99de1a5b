@echo off
echo ========================================
echo WebRTC 音视频流测试脚本
echo ========================================
echo.

echo 请选择测试模式:
echo 1. 彩色测试条纹 + 音频 (推荐)
echo 2. 桌面录制 + 系统音频
echo 3. 摄像头 + 麦克风 (需要设备)
echo 4. 循环播放视频文件 (需要指定文件)
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto test_pattern
if "%choice%"=="2" goto desktop_capture
if "%choice%"=="3" goto camera_capture
if "%choice%"=="4" goto file_playback

echo 无效选择，使用默认测试模式...

:test_pattern
echo.
echo 启动彩色测试条纹 + 音频流...
echo 目标: udp://127.0.0.1:1234
echo 按 Ctrl+C 停止推流
echo.
ffmpeg -f lavfi -i testsrc2=size=1920x1080:rate=30 -f lavfi -i sine=frequency=1000:sample_rate=48000 -c:v libx264 -preset ultrafast -tune zerolatency -profile:v baseline -level 3.1 -x264-params "bframes=0:scenecut=0:keyint=30:min-keyint=30" -c:a aac -b:a 128k -f mpegts udp://127.0.0.1:1234
goto end

:desktop_capture
echo.
echo 启动桌面录制...
echo 目标: udp://127.0.0.1:1234
echo 按 Ctrl+C 停止推流
echo.
ffmpeg -f gdigrab -framerate 30 -i desktop -c:v libx264 -preset ultrafast -tune zerolatency -profile:v baseline -level 3.1 -s 1920x1080 -f mpegts udp://127.0.0.1:1234
goto end

:camera_capture
echo.
echo 启动摄像头录制...
echo 注意: 需要先确认摄像头设备名称
echo 使用命令查看设备: ffmpeg -list_devices true -f dshow -i dummy
echo.
pause
echo 目标: udp://127.0.0.1:1234
echo 按 Ctrl+C 停止推流
echo.
ffmpeg -f dshow -i video="USB2.0 HD UVC WebCam" -c:v libx264 -preset ultrafast -tune zerolatency -profile:v baseline -level 3.1 -s 1920x1080 -f mpegts udp://127.0.0.1:1234
goto end

:file_playback
echo.
set /p filepath="请输入视频文件路径: "
if not exist "%filepath%" (
    echo 文件不存在: %filepath%
    pause
    goto end
)
echo.
echo 循环播放视频文件...
echo 目标: udp://127.0.0.1:1234
echo 按 Ctrl+C 停止推流
echo.
ffmpeg -stream_loop -1 -i "%filepath%" -c:v libx264 -preset ultrafast -tune zerolatency -profile:v baseline -level 3.1 -c:a aac -b:a 128k -f mpegts udp://127.0.0.1:1234
goto end

:end
echo.
echo 推流已停止
pause
