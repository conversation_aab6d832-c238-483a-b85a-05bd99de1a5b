#pragma once

#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <unordered_map>
#include <mutex>

class WebRTCPeer;

class SignalingServer {
public:
    using OnPeerConnectedCallback = std::function<void(std::shared_ptr<WebRTCPeer>)>;
    using OnPeerDisconnectedCallback = std::function<void(const std::string&)>;

    explicit SignalingServer(int port = 8080);
    ~SignalingServer();

    bool start();
    void stop();
    bool isRunning() const { return m_running; }

    void setOnPeerConnected(OnPeerConnectedCallback callback) { m_onPeerConnected = callback; }
    void setOnPeerDisconnected(OnPeerDisconnectedCallback callback) { m_onPeerDisconnected = callback; }

    std::shared_ptr<WebRTCPeer> getPeer(const std::string& peerId);
    size_t getConnectedPeerCount() const;

private:
    void runHttpServer();
    void handleWebSocketConnection(const std::string& peerId);
    void handleSignalingMessage(const std::string& peerId, const std::string& message);
    
    std::string getStaticFile(const std::string& path);
    std::string getMimeType(const std::string& extension);

    int m_port;
    std::atomic<bool> m_running{false};
    std::thread m_serverThread;
    
    std::unordered_map<std::string, std::shared_ptr<WebRTCPeer>> m_peers;
    mutable std::mutex m_peersMutex;
    
    OnPeerConnectedCallback m_onPeerConnected;
    OnPeerDisconnectedCallback m_onPeerDisconnected;
};
