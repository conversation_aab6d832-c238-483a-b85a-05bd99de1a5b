/**
 * 🎮 优化的H264播放器
 * 专注于性能和稳定性，简化实现
 */

import { ref, reactive } from 'vue'

// 播放器状态
const playerState = reactive({
  initialized: false,
  playing: false,
  error: null,
  stats: {
    totalFrames: 0,
    decodedFrames: 0,
    droppedFrames: 0,
    fps: 0,
    resolution: '等待视频流...'
  }
})

// 核心组件
let canvas = null
let ctx = null
let decoder = null
let isConfigured = false

// 性能统计
let frameCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

// 帧队列管理
const frameQueue = []
const MAX_QUEUE_SIZE = 5  // 减少队列大小提高响应性
let isProcessingQueue = false

/**
 * 初始化H264播放器
 * @param {HTMLCanvasElement} canvasElement - Canvas元素
 * @param {Object} options - 配置选项
 */
export async function initH264Player(canvasElement, options = {}) {
  if (!window.VideoDecoder) {
    throw new Error('浏览器不支持WebCodecs API，需要Chrome 94+')
  }

  try {
    canvas = canvasElement
    ctx = canvas.getContext('2d')
    
    if (!canvas || !ctx) {
      throw new Error('Canvas初始化失败')
    }

    // 设置Canvas样式
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    canvas.style.objectFit = 'contain'
    canvas.style.backgroundColor = '#000'

    // 创建解码器
    await createDecoder()

    playerState.initialized = true
    playerState.error = null
    
    console.log('H264播放器初始化成功')
    return true

  } catch (error) {
    playerState.error = error.message
    console.error('H264播放器初始化失败:', error)
    return false
  }
}

/**
 * 创建视频解码器
 */
async function createDecoder() {
  // 清理旧解码器
  if (decoder) {
    try {
      if (decoder.state !== 'closed') {
        decoder.close()
      }
    } catch (e) {
      console.warn('关闭旧解码器失败:', e)
    }
  }

  decoder = new VideoDecoder({
    output: handleDecodedFrame,
    error: handleDecoderError
  })

  // 尝试配置解码器 - 简化配置专注兼容性
  const configs = [
    {
      codec: 'avc1.42001E', // H264 Baseline Profile Level 3.0
      optimizeForLatency: true,
      hardwareAcceleration: 'prefer-software' // 优先软件解码避免硬件兼容问题
    },
    {
      codec: 'avc1.42001F', // H264 Baseline Profile Level 3.1
      optimizeForLatency: true,
      hardwareAcceleration: 'prefer-software'
    },
    {
      codec: 'avc1.420028', // H264 Baseline Profile Level 4.0
      optimizeForLatency: true,
      hardwareAcceleration: 'no-preference'
    }
  ]

  for (const config of configs) {
    try {
      // 检查配置支持
      const support = await VideoDecoder.isConfigSupported(config)
      if (!support.supported) {
        console.warn('配置不支持:', config.codec, support)
        continue
      }

      console.log('🔧 尝试配置解码器:', config.codec, '分辨率:', config.codedWidth + 'x' + config.codedHeight)
      decoder.configure(config)
      isConfigured = true
      console.log('✅ 解码器配置成功:', config.codec)
      break
    } catch (e) {
      console.warn(`配置失败 ${config.codec}:`, e.message)
    }
  }

  if (!isConfigured) {
    throw new Error('所有解码器配置都失败')
  }
}

/**
 * 处理解码后的帧
 * @param {VideoFrame} frame - 解码后的视频帧
 */
function handleDecodedFrame(frame) {
  try {
    if (!canvas || !ctx) {
      frame.close()
      return
    }

    // 动态调整Canvas尺寸
    if (canvas.width !== frame.displayWidth || canvas.height !== frame.displayHeight) {
      canvas.width = frame.displayWidth
      canvas.height = frame.displayHeight
      
      playerState.stats.resolution = `${frame.displayWidth}x${frame.displayHeight}`
      console.log(`视频尺寸: ${frame.displayWidth}x${frame.displayHeight}`)
    }

    // 渲染帧到Canvas
    requestAnimationFrame(() => {
      try {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(frame, 0, 0)
        frame.close()
        
        // 更新统计
        updateStats()
        
        if (!playerState.playing) {
          playerState.playing = true
          console.log('视频播放开始')
        }
      } catch (renderError) {
        console.error('渲染帧失败:', renderError)
        frame.close()
      }
    })

  } catch (error) {
    console.error('处理解码帧失败:', error)
    frame.close()
  }
}

/**
 * 处理解码器错误
 * @param {Error} error - 解码错误
 */
function handleDecoderError(error) {
  console.error('解码器错误:', error)
  playerState.error = error.message
  isConfigured = false
  
  // 尝试重新初始化
  setTimeout(() => {
    console.log('尝试重新初始化解码器...')
    createDecoder().catch(e => {
      console.error('重新初始化失败:', e)
    })
  }, 2000)
}

/**
 * 处理H264数据
 * @param {ArrayBuffer|Uint8Array} data - H264数据
 */
export function handleH264Data(data) {
  if (!decoder || !isConfigured) {
    console.warn('解码器未就绪，跳过帧')
    return false
  }

  try {
    const uint8Data = data instanceof Uint8Array ? data : new Uint8Array(data)
    
    if (uint8Data.length < 5) {
      console.warn('H264数据太短:', uint8Data.length)
      return false
    }

    // 检查NAL单元类型
    const isKeyFrame = checkKeyFrame(uint8Data)

    // 添加详细调试信息 - 正确解析NAL类型
    let nalType = 'unknown'
    for (let i = 0; i < uint8Data.length - 4; i++) {
      if (uint8Data[i] === 0x00 && uint8Data[i+1] === 0x00 &&
          uint8Data[i+2] === 0x00 && uint8Data[i+3] === 0x01) {
        if (i + 4 < uint8Data.length) {
          nalType = uint8Data[i + 4] & 0x1F
          break
        }
      }
    }
    // 只记录关键帧，减少日志输出
    if (isKeyFrame) {
      console.log('📹 H264关键帧:', uint8Data.length, '字节, NAL类型:', nalType)
    }

    // 生成时间戳
    const timestamp = performance.now() * 1000
    
    // 创建编码视频块
    const chunk = new EncodedVideoChunk({
      type: isKeyFrame ? 'key' : 'delta',
      timestamp: timestamp,
      data: uint8Data,
      duration: 33333 // 30fps
    })

    // 添加到队列
    frameQueue.push(chunk)
    
    // 限制队列大小 - 更智能的丢帧策略
    if (frameQueue.length > MAX_QUEUE_SIZE) {
      // 优先丢弃非关键帧
      let droppedIndex = -1
      for (let i = 0; i < frameQueue.length; i++) {
        if (frameQueue[i].type === 'delta') {
          droppedIndex = i
          break
        }
      }

      if (droppedIndex >= 0) {
        frameQueue.splice(droppedIndex, 1)
      } else {
        frameQueue.shift() // 如果都是关键帧，丢弃最老的
      }

      playerState.stats.droppedFrames++
      // console.warn('队列过长，智能丢帧') // 减少日志输出
    }

    // 处理队列
    processFrameQueue()
    
    return true

  } catch (error) {
    console.error('处理H264数据失败:', error)
    return false
  }
}

/**
 * 处理帧队列
 */
async function processFrameQueue() {
  if (isProcessingQueue || frameQueue.length === 0) {
    return
  }

  isProcessingQueue = true

  try {
    while (frameQueue.length > 0) {
      const chunk = frameQueue.shift()
      
      // 检查解码器队列大小
      if (decoder.decodeQueueSize > 3) {
        console.warn('解码器队列过长，等待处理')
        await new Promise(resolve => setTimeout(resolve, 16))
        continue
      }

      decoder.decode(chunk)
      
      // 控制解码速度
      if (frameQueue.length > 2) {
        await new Promise(resolve => setTimeout(resolve, 8))
      }
    }
  } catch (error) {
    console.error('处理帧队列失败:', error)
  } finally {
    isProcessingQueue = false
  }
}

/**
 * 检查是否为关键帧
 * @param {Uint8Array} data - H264数据
 */
function checkKeyFrame(data) {
  // 查找NAL起始码并检查类型
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i+1] === 0x00 && 
        data[i+2] === 0x00 && data[i+3] === 0x01) {
      if (i + 4 < data.length) {
        const nalType = data[i + 4] & 0x1F
        // IDR帧(5)、SPS(7)、PPS(8)都是关键帧
        if (nalType === 5 || nalType === 7 || nalType === 8) {
          return true
        }
      }
    }
  }
  return false
}

/**
 * 更新性能统计
 */
function updateStats() {
  frameCount++
  framesSinceLastFps++
  playerState.stats.totalFrames = frameCount
  playerState.stats.decodedFrames = frameCount - playerState.stats.droppedFrames

  const now = Date.now()
  if (now - lastFpsTime >= 1000) {
    playerState.stats.fps = framesSinceLastFps
    framesSinceLastFps = 0
    lastFpsTime = now
  }
}

/**
 * 获取播放器状态
 */
export function getPlayerState() {
  return playerState
}

/**
 * 重置播放器
 */
export async function resetPlayer() {
  if (decoder && decoder.state !== 'closed') {
    decoder.close()
  }
  
  isConfigured = false
  frameCount = 0
  framesSinceLastFps = 0
  frameQueue.length = 0
  
  playerState.playing = false
  playerState.stats.totalFrames = 0
  playerState.stats.decodedFrames = 0
  playerState.stats.droppedFrames = 0
  playerState.stats.fps = 0
  
  if (canvas && ctx) {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    await createDecoder()
  }
}

/**
 * 清理播放器
 */
export function cleanupPlayer() {
  if (decoder) {
    try {
      if (decoder.state !== 'closed') {
        decoder.close()
      }
    } catch (e) {
      console.warn('清理解码器失败:', e)
    }
    decoder = null
  }

  canvas = null
  ctx = null
  isConfigured = false
  frameQueue.length = 0
  
  playerState.initialized = false
  playerState.playing = false
  playerState.error = null
}
