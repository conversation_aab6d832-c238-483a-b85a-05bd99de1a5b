/**
 * 🖱️ 输入事件捕获系统
 * 精确捕获鼠标和键盘事件，与libdatachannel后端兼容
 */

import { ref } from 'vue'

// 输入状态
const inputState = ref({
  enabled: true,
  capturing: false,
  stats: {
    mouseEvents: 0,
    keyboardEvents: 0,
    lastEventTime: null
  }
})

// 事件发送回调
let sendEventCallback = null

// 鼠标移动节流
let lastMouseMoveTime = 0
const MOUSE_MOVE_THROTTLE = 16 // 约60fps

/**
 * 初始化输入捕获
 * @param {HTMLElement} targetElement - 目标元素
 * @param {Function} sendCallback - 发送事件的回调函数
 */
export function initInputCapture(targetElement, sendCallback) {
  if (!targetElement) {
    throw new Error('目标元素不能为空')
  }

  sendEventCallback = sendCallback
  
  // 设置元素属性以捕获键盘事件
  targetElement.tabIndex = 0
  targetElement.style.outline = 'none'
  
  // 添加事件监听器
  setupMouseEvents(targetElement)
  setupKeyboardEvents(targetElement)
  
  inputState.value.capturing = true
  console.log('输入捕获系统已初始化')
}

/**
 * 设置鼠标事件监听器
 * @param {HTMLElement} element - 目标元素
 */
function setupMouseEvents(element) {
  // 鼠标移动
  element.addEventListener('mousemove', (event) => {
    if (!inputState.value.enabled) return
    
    // 节流处理
    const now = performance.now()
    if (now - lastMouseMoveTime < MOUSE_MOVE_THROTTLE) {
      return
    }
    lastMouseMoveTime = now
    
    const coords = getRelativeCoordinates(event, element)
    sendInputEvent({
      type: 'mousemove',
      x: coords.x,
      y: coords.y,
      timestamp: Date.now()
    })
  })

  // 鼠标按下
  element.addEventListener('mousedown', (event) => {
    if (!inputState.value.enabled) return
    event.preventDefault()
    
    const coords = getRelativeCoordinates(event, element)
    sendInputEvent({
      type: 'mousedown',
      x: coords.x,
      y: coords.y,
      button: event.button,
      timestamp: Date.now()
    })
  })

  // 鼠标释放
  element.addEventListener('mouseup', (event) => {
    if (!inputState.value.enabled) return
    event.preventDefault()
    
    const coords = getRelativeCoordinates(event, element)
    sendInputEvent({
      type: 'mouseup',
      x: coords.x,
      y: coords.y,
      button: event.button,
      timestamp: Date.now()
    })
  })

  // 鼠标点击（用于获取焦点）
  element.addEventListener('click', (event) => {
    element.focus()
    
    if (!inputState.value.enabled) return
    event.preventDefault()
    
    const coords = getRelativeCoordinates(event, element)
    sendInputEvent({
      type: 'click',
      x: coords.x,
      y: coords.y,
      button: event.button,
      timestamp: Date.now()
    })
  })

  // 鼠标滚轮
  element.addEventListener('wheel', (event) => {
    if (!inputState.value.enabled) return
    event.preventDefault()
    
    const coords = getRelativeCoordinates(event, element)
    sendInputEvent({
      type: 'wheel',
      x: coords.x,
      y: coords.y,
      deltaX: event.deltaX,
      deltaY: event.deltaY,
      deltaZ: event.deltaZ,
      timestamp: Date.now()
    })
  })

  // 右键菜单
  element.addEventListener('contextmenu', (event) => {
    if (!inputState.value.enabled) return
    event.preventDefault()
    
    const coords = getRelativeCoordinates(event, element)
    sendInputEvent({
      type: 'contextmenu',
      x: coords.x,
      y: coords.y,
      timestamp: Date.now()
    })
  })
}

/**
 * 设置键盘事件监听器
 * @param {HTMLElement} element - 目标元素
 */
function setupKeyboardEvents(element) {
  // 键盘按下
  element.addEventListener('keydown', (event) => {
    if (!inputState.value.enabled) return
    event.preventDefault()
    
    sendInputEvent({
      type: 'keydown',
      key: event.key,
      code: event.code,
      keyCode: event.keyCode,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      altKey: event.altKey,
      metaKey: event.metaKey,
      repeat: event.repeat,
      timestamp: Date.now()
    })
  })

  // 键盘释放
  element.addEventListener('keyup', (event) => {
    if (!inputState.value.enabled) return
    event.preventDefault()
    
    sendInputEvent({
      type: 'keyup',
      key: event.key,
      code: event.code,
      keyCode: event.keyCode,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      altKey: event.altKey,
      metaKey: event.metaKey,
      timestamp: Date.now()
    })
  })
}

/**
 * 获取相对坐标
 * @param {MouseEvent} event - 鼠标事件
 * @param {HTMLElement} element - 目标元素
 */
function getRelativeCoordinates(event, element) {
  const rect = element.getBoundingClientRect()
  
  // 计算相对于元素的坐标
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  // 转换为远程桌面坐标（假设远程桌面是1920x1080）
  const remoteX = Math.round((x / rect.width) * 1920)
  const remoteY = Math.round((y / rect.height) * 1080)
  
  return {
    x: Math.max(0, Math.min(1920, remoteX)),
    y: Math.max(0, Math.min(1080, remoteY))
  }
}

/**
 * 发送输入事件
 * @param {Object} eventData - 事件数据
 */
function sendInputEvent(eventData) {
  if (!sendEventCallback) {
    console.warn('发送回调未设置')
    return
  }

  try {
    sendEventCallback(eventData)
    
    // 更新统计
    if (eventData.type.startsWith('mouse') || eventData.type === 'click' || eventData.type === 'wheel') {
      inputState.value.stats.mouseEvents++
    } else if (eventData.type.startsWith('key')) {
      inputState.value.stats.keyboardEvents++
    }
    
    inputState.value.stats.lastEventTime = eventData.timestamp
    
  } catch (error) {
    console.error('发送输入事件失败:', error)
  }
}

/**
 * 启用/禁用输入捕获
 * @param {boolean} enabled - 是否启用
 */
export function setInputEnabled(enabled) {
  inputState.value.enabled = enabled
  console.log('输入捕获', enabled ? '已启用' : '已禁用')
}

/**
 * 获取输入状态
 */
export function getInputState() {
  return inputState.value
}

/**
 * 重置统计信息
 */
export function resetInputStats() {
  inputState.value.stats = {
    mouseEvents: 0,
    keyboardEvents: 0,
    lastEventTime: null
  }
}

/**
 * 清理输入捕获
 */
export function cleanupInputCapture() {
  sendEventCallback = null
  inputState.value.capturing = false
  inputState.value.enabled = false
  resetInputStats()
  console.log('输入捕获系统已清理')
}
