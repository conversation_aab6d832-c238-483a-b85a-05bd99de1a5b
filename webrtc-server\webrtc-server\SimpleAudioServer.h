#pragma once

#include <rtc/rtc.hpp>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <unordered_map>

class LibDataChannelAudioServer {
public:
    LibDataChannelAudioServer();
    ~LibDataChannelAudioServer();

    bool start(int port = 8081);
    void stop();
    void sendAudioData(const uint8_t* data, size_t size);
    bool isRunning() const { return m_running; }
    int getClientCount() const { return m_clientCount; }

private:
    void onClientConnected(std::shared_ptr<rtc::WebSocket> ws);

    int m_port;
    std::shared_ptr<rtc::WebSocketServer> m_wsServer;
    std::atomic<bool> m_running{false};
    std::atomic<int> m_clientCount{0};

    std::vector<std::shared_ptr<rtc::WebSocket>> m_clients;
    std::mutex m_clientsMutex;
};
