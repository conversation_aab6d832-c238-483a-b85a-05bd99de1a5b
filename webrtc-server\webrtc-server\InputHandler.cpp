#include "InputHandler.h"
#include "json.hpp"
#include <iostream>
#include <unordered_map>
#include <iomanip>
#include <cmath>
#include <vector>

using json = nlohmann::json;

InputHandler::InputHandler() {
    std::cout << "[InputHandler] Creating input event handler" << std::endl;
}

InputHandler::~InputHandler() {
    cleanup();
}

bool InputHandler::initialize() {
    std::cout << "[InputHandler] Initializing input event handler..." << std::endl;

    try {
        std::cout << "[InputHandler] Input handler initialized successfully" << std::endl;

        m_initialized = true;
        std::cout << "[InputHandler] Input event handler initialized successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[InputHandler] Failed to initialize input handler: " << e.what() << std::endl;
        return false;
    }
}

void InputHandler::cleanup() {
    if (!m_initialized) return;

    std::cout << "[InputHandler] Cleaning up input event handler..." << std::endl;
    m_initialized = false;
    std::cout << "[InputHandler] Input event handler cleaned up successfully" << std::endl;
}

void InputHandler::update() {
    // Periodic maintenance logic can be added here
}

void InputHandler::processEvent(const std::string& eventData) {
    if (!m_initialized) return;

    try {
        json event = json::parse(eventData);
        std::string type = event["type"];

        // Handle specific mouse events
        if (type == "mousemove" || type == "mousedown" || type == "mouseup" || type == "click" || type == "contextmenu") {
            handleMouseEvent(eventData);
        }
        // Handle specific keyboard events
        else if (type == "keydown" || type == "keyup") {
            handleKeyboardEvent(eventData);
        }
        // Handle wheel events
        else if (type == "wheel") {
            handleWheelEvent(eventData);
        }
        // Legacy support for generic types
        else if (type == "mouse") {
            handleMouseEvent(eventData);
        } else if (type == "keyboard") {
            handleKeyboardEvent(eventData);
        } else {
            std::cerr << "[InputHandler] Unsupported event type: " << type << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "[InputHandler] Failed to process event: " << e.what() << std::endl;
    }
}

void InputHandler::handleMouseEvent(const std::string& eventData) {
    try {
        json event = json::parse(eventData);
        std::string type = event["type"];

        long long timestamp = 0;
        if (event.contains("timestamp")) {
            timestamp = event["timestamp"];
        }

        std::cout << "[InputHandler] Mouse Event Details:" << std::endl;
        std::cout << "  Type: " << type << std::endl;
        std::cout << "  Timestamp: " << timestamp << "ms" << std::endl;

        if (type == "mousemove") {
            int x = event["x"];
            int y = event["y"];

            double normalizedX = static_cast<double>(x) / 1920.0;
            double normalizedY = static_cast<double>(y) / 1080.0;

            std::cout << "  Absolute Position: (" << x << ", " << y << ")" << std::endl;
            std::cout << "  Normalized Position: (" << std::fixed << std::setprecision(4)
                      << normalizedX << ", " << normalizedY << ")" << std::endl;

        } else if (type == "mousedown" || type == "mouseup" || type == "click") {
            int x = event["x"];
            int y = event["y"];
            int button = event["button"];

            std::string buttonName;
            switch(button) {
                case 0: buttonName = "Left"; break;
                case 1: buttonName = "Middle"; break;
                case 2: buttonName = "Right"; break;
                default: buttonName = "Unknown(" + std::to_string(button) + ")"; break;
            }

            double normalizedX = static_cast<double>(x) / 1920.0;
            double normalizedY = static_cast<double>(y) / 1080.0;

            std::cout << "  Position: (" << x << ", " << y << ")" << std::endl;
            std::cout << "  Normalized: (" << std::fixed << std::setprecision(4)
                      << normalizedX << ", " << normalizedY << ")" << std::endl;
            std::cout << "  Button: " << buttonName << " (" << button << ")" << std::endl;

        } else if (type == "contextmenu") {
            int x = event["x"];
            int y = event["y"];
            double normalizedX = static_cast<double>(x) / 1920.0;
            double normalizedY = static_cast<double>(y) / 1080.0;

            std::cout << "  Position: (" << x << ", " << y << ")" << std::endl;
            std::cout << "  Normalized: (" << std::fixed << std::setprecision(4)
                      << normalizedX << ", " << normalizedY << ")" << std::endl;
            std::cout << "  Action: Right-click context menu" << std::endl;
        }

        std::cout << "  ---" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[InputHandler] Failed to handle mouse event: " << e.what() << std::endl;
    }
}

void InputHandler::handleKeyboardEvent(const std::string& eventData) {
    try {
        json event = json::parse(eventData);
        std::string type = event["type"];
        std::string key = event["key"];
        std::string code = event["code"];

        long long timestamp = 0;
        if (event.contains("timestamp")) {
            timestamp = event["timestamp"];
        }

        int keyCode = 0;
        if (event.contains("keyCode")) {
            keyCode = event["keyCode"];
        }

        bool ctrlKey = event.contains("ctrlKey") && event["ctrlKey"];
        bool shiftKey = event.contains("shiftKey") && event["shiftKey"];
        bool altKey = event.contains("altKey") && event["altKey"];
        bool metaKey = event.contains("metaKey") && event["metaKey"];

        std::cout << "[InputHandler] Keyboard Event Details:" << std::endl;
        std::cout << "  Type: " << type << std::endl;
        std::cout << "  Timestamp: " << timestamp << "ms" << std::endl;
        std::cout << "  Key: '" << key << "'" << std::endl;
        std::cout << "  Code: " << code << std::endl;
        std::cout << "  KeyCode: " << keyCode << std::endl;

        std::cout << "  Modifiers: ";
        std::vector<std::string> modifiers;
        if (ctrlKey) modifiers.push_back("Ctrl");
        if (shiftKey) modifiers.push_back("Shift");
        if (altKey) modifiers.push_back("Alt");
        if (metaKey) modifiers.push_back("Meta/Win");

        if (modifiers.empty()) {
            std::cout << "None";
        } else {
            for (size_t i = 0; i < modifiers.size(); ++i) {
                if (i > 0) std::cout << " + ";
                std::cout << modifiers[i];
            }
        }
        std::cout << std::endl;

        if (!modifiers.empty()) {
            std::cout << "  Combination: ";
            for (size_t i = 0; i < modifiers.size(); ++i) {
                if (i > 0) std::cout << " + ";
                std::cout << modifiers[i];
            }
            std::cout << " + " << key << std::endl;
        }
        if (key.length() > 1) {  
            std::cout << "  Special Key: " << key << std::endl;
        }

        std::cout << "  ---" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[InputHandler] Failed to handle keyboard event: " << e.what() << std::endl;
    }
}

void InputHandler::handleWheelEvent(const std::string& eventData) {
    try {
        json event = json::parse(eventData);

        double deltaX = 0.0;
        if (event.contains("deltaX")) {
            deltaX = event["deltaX"];
        }

        double deltaY = 0.0;
        if (event.contains("deltaY")) {
            deltaY = event["deltaY"];
        }

        double deltaZ = 0.0;
        if (event.contains("deltaZ")) {
            deltaZ = event["deltaZ"];
        }

        int x = 0;
        if (event.contains("x")) {
            x = event["x"];
        }

        int y = 0;
        if (event.contains("y")) {
            y = event["y"];
        }

        long long timestamp = 0;
        if (event.contains("timestamp")) {
            timestamp = event["timestamp"];
        }

        std::cout << "[InputHandler] Wheel Event Details:" << std::endl;
        std::cout << "  Timestamp: " << timestamp << "ms" << std::endl;
        std::cout << "  Position: (" << x << ", " << y << ")" << std::endl;

        double normalizedX = static_cast<double>(x) / 1920.0;
        double normalizedY = static_cast<double>(y) / 1080.0;
        std::cout << "  Normalized: (" << std::fixed << std::setprecision(4)
                  << normalizedX << ", " << normalizedY << ")" << std::endl;

        std::cout << "  Delta: X=" << deltaX << ", Y=" << deltaY << ", Z=" << deltaZ << std::endl;

        if (deltaY != 0) {
            std::cout << "  Vertical Scroll: " << (deltaY > 0 ? "Down" : "Up")
                      << " (intensity: " << std::abs(deltaY) << ")" << std::endl;
        }
        if (deltaX != 0) {
            std::cout << "  Horizontal Scroll: " << (deltaX > 0 ? "Right" : "Left")
                      << " (intensity: " << std::abs(deltaX) << ")" << std::endl;
        }

        std::cout << "  ---" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[InputHandler] Failed to handle wheel event: " << e.what() << std::endl;
    }
}




