#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <signal.h>
#include <conio.h>

#include "SignalingServer.h"
#include "WebRTCPeer.h"
#include "FFmpegDecoder.h"
#include "InputHandler.h"


// Global variable for graceful shutdown
std::atomic<bool> g_running{true};

void signalHandler(int signal) {
    std::cout << "\n[Main] Received signal " << signal << ", shutting down server..." << std::endl;
    g_running = false;
}

int main() {
    // Set console encoding to UTF-8
    system("chcp 65001");

    std::cout << "=== WebRTC Audio/Video Streaming Server ===" << std::endl;
    std::cout << "Based on libdatachannel + FFmpeg" << std::endl;
    std::cout << "===========================================" << std::endl;

    // Register signal handlers
    signal(<PERSON><PERSON><PERSON><PERSON>, signalHandler);
    signal(<PERSON>IG<PERSON>R<PERSON>, signalHandler);

    try {
        // 1. Initialize signaling server
        auto signalingServer = std::make_unique<SignalingServer>(8080);
        if (!signalingServer->start()) {
            std::cerr << "[Main] Failed to start signaling server" << std::endl;
            return -1;
        }
        std::cout << "[Main] Signaling server started successfully (port: 8080)" << std::endl;

        // 2. Initialize FFmpeg decoder
        auto ffmpegDecoder = std::make_unique<FFmpegDecoder>();
        if (!ffmpegDecoder->initialize()) {
            std::cerr << "[Main] Failed to initialize FFmpeg decoder" << std::endl;
            return -1;
        }
        std::cout << "[Main] FFmpeg decoder initialized successfully" << std::endl;

        // 3. Audio will be sent via WebRTC DataChannel (no separate server needed)
        std::cout << "[Main] Audio will be transmitted via WebRTC DataChannel" << std::endl;

        // 4. Initialize input event handler
        auto inputHandler = std::make_unique<InputHandler>();
        if (!inputHandler->initialize()) {
            std::cerr << "[Main] Failed to initialize input handler" << std::endl;
            return -1;
        }
        std::cout << "[Main] Input handler initialized successfully" << std::endl;

        // 5. Setup WebRTC connection callbacks
        signalingServer->setOnPeerConnected([&](std::shared_ptr<WebRTCPeer> peer) {
            std::cout << "[Main] New WebRTC connection: " << peer->getId() << std::endl;

            // Connect FFmpeg decoder to WebRTC peer (both video and audio)
            ffmpegDecoder->setOutputCallback([peer](const uint8_t* data, size_t size, bool isVideo) {
                if (isVideo) {
                    peer->sendVideoFrame(data, size);
                } else {
                    // Send audio data via WebRTC DataChannel
                    peer->sendAudioFrame(data, size);
                }
            });

            // Connect input event handling
            peer->setInputEventCallback([&inputHandler](const std::string& eventData) {
                inputHandler->processEvent(eventData);
            });
        });

        // 5. Start FFmpeg stream receiving
        std::cout << "[Main] Starting FFmpeg stream receiver..." << std::endl;
        if (!ffmpegDecoder->startReceiving("udp://127.0.0.1:1235")) {
            std::cout << "[Main] Warning: Failed to start FFmpeg stream receiving. You can start it later." << std::endl;
        } else {
            std::cout << "[Main] FFmpeg decoder started, listening for UDP stream on 127.0.0.1:1235" << std::endl;
        }

        // 6. Start audio stream receiving
        std::cout << "[Main] Starting audio stream receiver..." << std::endl;
        if (!ffmpegDecoder->startAudioReceiving("udp://127.0.0.1:1236")) {
            std::cout << "[Main] Warning: Failed to start audio stream receiving." << std::endl;
        } else {
            std::cout << "[Main] Audio receiver started, listening for PCM audio on 127.0.0.1:1236" << std::endl;
        }

        std::cout << "[Main] Server startup complete!" << std::endl;
        std::cout << "[Main] Access URL: http://localhost:8080" << std::endl;
        std::cout << "[Main] FFmpeg Stream: udp://127.0.0.1:1235" << std::endl;
        std::cout << "[Main] Commands:" << std::endl;
        std::cout << "[Main]   r - Restart FFmpeg stream receiver" << std::endl;
        std::cout << "[Main]   q - Quit server" << std::endl;
        std::cout << "[Main] Press Ctrl+C to exit" << std::endl;

        // Main loop
        while (g_running) {
            // Check for keyboard input
            if (_kbhit()) {
                char key = _getch();
                if (key == 'r' || key == 'R') {
                    std::cout << "[Main] Restarting FFmpeg stream receiver..." << std::endl;
                    ffmpegDecoder->stopReceiving();
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    if (ffmpegDecoder->startReceiving("udp://127.0.0.1:1235")) {
                        std::cout << "[Main] FFmpeg stream receiver restarted successfully" << std::endl;
                    } else {
                        std::cout << "[Main] Failed to restart FFmpeg stream receiver" << std::endl;
                    }
                } else if (key == 'q' || key == 'Q') {
                    std::cout << "[Main] Shutting down server..." << std::endl;
                    g_running = false;
                    break;
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            // Periodically update components
            ffmpegDecoder->update();
            inputHandler->update();
        }

        std::cout << "[Main] Shutting down server..." << std::endl;

        // Graceful shutdown
        signalingServer->stop();
        ffmpegDecoder->cleanup();
        inputHandler->cleanup();

        std::cout << "[Main] Server shutdown complete" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[Main] Exception: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}