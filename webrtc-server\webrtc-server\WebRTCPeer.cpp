#include "WebRTCPeer.h"
#include <iostream>
#include <chrono>
#include <vector>

WebRTCPeer::WebRTCPeer(const std::string& id) : m_id(id) {
    std::cout << "[WebRTCPeer:" << m_id << "] Creating WebRTC connection" << std::endl;
    setupPeerConnection();
}

WebRTCPeer::~WebRTCPeer() {
    std::cout << "[WebRTCPeer:" << m_id << "] Destroying WebRTC connection" << std::endl;
    
    if (m_peerConnection) {
        m_peerConnection->close();
    }
}

void WebRTCPeer::setupPeerConnection() {
    try {
        // Create WebRTC configuration
        rtc::Configuration config;

        // Add STUN server
        config.iceServers.emplace_back("stun:stun.l.google.com:19302");

        // Create PeerConnection
        m_peerConnection = std::make_shared<rtc::PeerConnection>(config);

        // Set connection state callback
        m_peerConnection->onStateChange([this](rtc::PeerConnection::State state) {
            onConnectionStateChange(state);
        });

        // Set ICE candidate callback
        m_peerConnection->onLocalCandidate([this](const rtc::Candidate& candidate) {
            std::cout << "[WebRTCPeer:" << m_id << "] Local ICE candidate: " << std::string(candidate) << std::endl;
        });

        // Setup data channels only - media tracks will be created when processing offer
        setupDataChannels();
        // Note: setupMediaTracks() moved to handleOffer() to avoid duplicate m-lines

        std::cout << "[WebRTCPeer:" << m_id << "] PeerConnection created successfully" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[WebRTCPeer:" << m_id << "] Failed to create PeerConnection: " << e.what() << std::endl;
        throw;
    }
}

void WebRTCPeer::setupDataChannels() {
    try {
        // Create input event data channel
        m_inputChannel = m_peerConnection->createDataChannel("input");
        m_inputChannel->onOpen([this]() {
            onDataChannelOpen(m_inputChannel);
        });
        m_inputChannel->onMessage([this](rtc::message_variant data) {
            if (std::holds_alternative<std::string>(data)) {
                onDataChannelMessage(m_inputChannel, std::get<std::string>(data));
            }
        });

        // Create video data channel
        m_videoChannel = m_peerConnection->createDataChannel("video");
        m_videoChannel->onOpen([this]() {
            onDataChannelOpen(m_videoChannel);
        });

        // Create audio data channel
        m_audioChannel = m_peerConnection->createDataChannel("audio");
        m_audioChannel->onOpen([this]() {
            onDataChannelOpen(m_audioChannel);
        });

        std::cout << "[WebRTCPeer:" << m_id << "] Data channels created successfully" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[WebRTCPeer:" << m_id << "] Failed to create data channels: " << e.what() << std::endl;
        throw;
    }
}

void WebRTCPeer::setupMediaTracks() {
    try {
        // Create video track
        rtc::Description::Video videoDesc("video");
        videoDesc.addH264Codec(96);  // H.264 encoding
        m_videoTrack = m_peerConnection->addTrack(videoDesc);

        // Set video track open callback
        m_videoTrack->onOpen([this]() {
            std::cout << "[WebRTCPeer:" << m_id << "] Video track opened and ready" << std::endl;
        });

        // Create audio track
        rtc::Description::Audio audioDesc("audio");
        audioDesc.addOpusCodec(111);  // Opus encoding
        m_audioTrack = m_peerConnection->addTrack(audioDesc);

        // Set audio track open callback
        m_audioTrack->onOpen([this]() {
            std::cout << "[WebRTCPeer:" << m_id << "] Audio track opened and ready" << std::endl;
        });

        std::cout << "[WebRTCPeer:" << m_id << "] Media tracks created successfully" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[WebRTCPeer:" << m_id << "] Failed to create media tracks: " << e.what() << std::endl;
        throw;
    }
}

bool WebRTCPeer::handleOffer(const std::string& offer) {
    try {
        std::cout << "[WebRTCPeer:" << m_id << "] Processing offer..." << std::endl;

        // Set remote description first
        rtc::Description remoteDesc(offer, rtc::Description::Type::Offer);
        m_peerConnection->setRemoteDescription(remoteDesc);

        // Now create media tracks to match the offer
        setupMediaTracks();

        // Create answer
        auto localDesc = m_peerConnection->localDescription();
        if (localDesc) {
            m_answer = std::string(*localDesc);
            std::cout << "[WebRTCPeer:" << m_id << "] Answer created successfully" << std::endl;
            return true;
        } else {
            std::cerr << "[WebRTCPeer:" << m_id << "] Failed to create answer" << std::endl;
            return false;
        }

    } catch (const std::exception& e) {
        std::cerr << "[WebRTCPeer:" << m_id << "] Failed to process offer: " << e.what() << std::endl;
        return false;
    }
}

void WebRTCPeer::addIceCandidate(const std::string& candidate) {
    try {
        rtc::Candidate iceCandidate(candidate);
        m_peerConnection->addRemoteCandidate(iceCandidate);
        std::cout << "[WebRTCPeer:" << m_id << "] ICE candidate added successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "[WebRTCPeer:" << m_id << "] Failed to add ICE candidate: " << e.what() << std::endl;
    }
}

void WebRTCPeer::sendVideoFrame(const uint8_t* data, size_t size) {
    std::lock_guard<std::mutex> lock(m_mutex);


    if (m_videoChannel && m_videoChannel->isOpen()) {
        try {
            const size_t MAX_CHUNK_SIZE = 16000;  // 16KB chunks to stay under DataChannel limits

            if (size <= MAX_CHUNK_SIZE) {
       
                std::vector<uint8_t> packet;
                packet.push_back(0x01);  
                packet.insert(packet.end(), data, data + size);

                m_videoChannel->send(reinterpret_cast<const std::byte*>(packet.data()), packet.size());
            } else {

                static uint32_t frameId = 0;
                frameId++;

                size_t offset = 0;
                uint16_t chunkIndex = 0;
                uint16_t totalChunks = (size + MAX_CHUNK_SIZE - 1) / MAX_CHUNK_SIZE;

                while (offset < size) {
                    size_t chunkSize = std::min(MAX_CHUNK_SIZE, size - offset);

                    //[0x03][frameId:4][chunkIndex:2][totalChunks:2][data...]
                    std::vector<uint8_t> packet;
                    packet.push_back(0x03);  

                    // frameId (4 bytes)
                    packet.push_back((frameId >> 24) & 0xFF);
                    packet.push_back((frameId >> 16) & 0xFF);
                    packet.push_back((frameId >> 8) & 0xFF);
                    packet.push_back(frameId & 0xFF);

                    // chunkIndex (2 bytes)
                    packet.push_back((chunkIndex >> 8) & 0xFF);
                    packet.push_back(chunkIndex & 0xFF);

                    // totalChunks (2 bytes)
                    packet.push_back((totalChunks >> 8) & 0xFF);
                    packet.push_back(totalChunks & 0xFF);

                    packet.insert(packet.end(), data + offset, data + offset + chunkSize);

                    m_videoChannel->send(reinterpret_cast<const std::byte*>(packet.data()), packet.size());

                    offset += chunkSize;
                    chunkIndex++;
                }
            }

            static int frameCount = 0;
            if (++frameCount % 100 == 0) {
                std::cout << "[WebRTCPeer:" << m_id << "] Sent " << frameCount << " video frames via DataChannel (size: " << size << " bytes)" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "[WebRTCPeer:" << m_id << "] Failed to send video frame: " << e.what() << std::endl;
        }
    }
}

void WebRTCPeer::sendAudioFrame(const uint8_t* data, size_t size) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_audioChannel && m_audioChannel->isOpen()) {
        try {
            std::vector<uint8_t> packet;
            packet.push_back(0x02); 
            packet.insert(packet.end(), data, data + size);

            m_audioChannel->send(reinterpret_cast<const std::byte*>(packet.data()), packet.size());

            static int frameCount = 0;
            if (++frameCount % 100 == 0) {
                std::cout << "[WebRTCPeer:" << m_id << "] Sent " << frameCount << " audio frames via DataChannel" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "[WebRTCPeer:" << m_id << "] Failed to send audio frame: " << e.what() << std::endl;
        }
    }
}

void WebRTCPeer::onDataChannelOpen(std::shared_ptr<rtc::DataChannel> channel) {
    std::cout << "[WebRTCPeer:" << m_id << "] Data channel opened: " << channel->label() << std::endl;

    if (channel->label() == "input") {
        // Input channel opened, ready to receive mouse/keyboard events
        std::cout << "[WebRTCPeer:" << m_id << "] Input event channel ready" << std::endl;
    }
}

void WebRTCPeer::onDataChannelMessage(std::shared_ptr<rtc::DataChannel> channel, const std::string& message) {
    if (channel->label() == "input" && m_inputEventCallback) {
        // Process input events
        m_inputEventCallback(message);
    }
}

void WebRTCPeer::onConnectionStateChange(rtc::PeerConnection::State state) {
    std::cout << "[WebRTCPeer:" << m_id << "] Connection state changed: ";
    
    switch (state) {
        case rtc::PeerConnection::State::New:
            std::cout << "New" << std::endl;
            break;
        case rtc::PeerConnection::State::Connecting:
            std::cout << "Connecting" << std::endl;
            break;
        case rtc::PeerConnection::State::Connected:
            std::cout << "Connected" << std::endl;
            m_connected = true;
            break;
        case rtc::PeerConnection::State::Disconnected:
            std::cout << "Disconnected" << std::endl;
            m_connected = false;
            break;
        case rtc::PeerConnection::State::Failed:
            std::cout << "Failed" << std::endl;
            m_connected = false;
            break;
        case rtc::PeerConnection::State::Closed:
            std::cout << "Closed" << std::endl;
            m_connected = false;
            break;
    }
}
