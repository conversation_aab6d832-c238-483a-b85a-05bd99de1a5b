@echo off
echo ========================================
echo 简化版 WebRTC 音视频流测试
echo ========================================
echo.

echo 使用更兼容的H264参数进行测试...
echo 目标: udp://127.0.0.1:1234
echo 按 Ctrl+C 停止推流
echo.

REM 使用更简单、更兼容的H264参数
ffmpeg -f lavfi -i testsrc2=size=640x480:rate=15 -f lavfi -i sine=frequency=440:sample_rate=44100 -c:v libx264 -preset veryfast -tune zerolatency -profile:v baseline -level 3.0 -pix_fmt yuv420p -g 30 -keyint_min 30 -sc_threshold 0 -b:v 1000k -maxrate 1000k -bufsize 2000k -x264-params "nal-hrd=cbr:force-cfr=1:bframes=0:ref=1" -c:a aac -profile:a aac_low -b:a 96k -ar 44100 -ac 1 -f mpegts udp://127.0.0.1:1235

echo.
echo 推流已停止
pause
