@echo off
echo ========================================
echo 创建测试视频文件
echo ========================================
echo.

echo 正在创建一个30秒的测试视频...
echo 包含彩色条纹和立体声音频

ffmpeg -f lavfi -i testsrc2=size=640x480:rate=30 -f lavfi -i sine=frequency=440:sample_rate=44100 -f lavfi -i sine=frequency=880:sample_rate=44100 -filter_complex "[1:a][2:a]amerge=inputs=2[aout]" -map 0:v -map "[aout]" -c:v libx264 -preset medium -profile:v baseline -level 3.0 -pix_fmt yuv420p -c:a aac -b:a 128k -ar 44100 -ac 2 -t 30 test.mp4

if exist "test.mp4" (
    echo.
    echo ✅ 测试视频创建成功: test.mp4
    echo 文件包含:
    echo - 30秒彩色条纹视频 (640x480, 30fps)
    echo - 立体声音频 (440Hz左声道 + 880Hz右声道)
    echo.
    echo 现在可以运行 stream_video.bat 进行推流测试
) else (
    echo ❌ 测试视频创建失败
)

echo.
pause
