#include "SimpleAudioServer.h"
#include <iostream>
#include <algorithm>

LibDataChannelAudioServer::LibDataChannelAudioServer() : m_port(8081) {
    std::cout << "[LibDataChannelAudioServer] Creating libdatachannel audio server" << std::endl;
}

LibDataChannelAudioServer::~LibDataChannelAudioServer() {
    stop();
}

bool LibDataChannelAudioServer::start(int port) {
    m_port = port;

    try {
        rtc::WebSocketServerConfiguration config;
        config.port = port;
        config.enableTls = false;

        m_wsServer = std::make_shared<rtc::WebSocketServer>(config);

        m_wsServer->onClient([this](std::shared_ptr<rtc::WebSocket> ws) {
            this->onClientConnected(ws);
        });

        m_running = true;
        std::cout << "[LibDataChannelAudioServer] WebSocket server started on port " << port << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[LibDataChannelAudioServer] Exception: " << e.what() << std::endl;
        return false;
    }
}

void LibDataChannelAudioServer::stop() {
    if (m_running) {
        m_running = false;

        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            m_clients.clear();
            m_clientCount = 0;
        }

        if (m_wsServer) {
            m_wsServer.reset();
        }

        std::cout << "[LibDataChannelAudioServer] Server stopped" << std::endl;
    }
}

void LibDataChannelAudioServer::onClientConnected(std::shared_ptr<rtc::WebSocket> ws) {
    std::cout << "[LibDataChannelAudioServer] New WebSocket client connected" << std::endl;

    try {
        // Set callbacks
        ws->onOpen([this, ws]() {
            std::cout << "[LibDataChannelAudioServer] WebSocket opened" << std::endl;
        });

        ws->onClosed([this, ws]() {
            std::cout << "[LibDataChannelAudioServer] WebSocket closed" << std::endl;
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            auto it = std::find(m_clients.begin(), m_clients.end(), ws);
            if (it != m_clients.end()) {
                m_clients.erase(it);
            }
            m_clientCount = m_clients.size();
        });

        ws->onError([this, ws](const std::string& error) {
            std::cout << "[LibDataChannelAudioServer] WebSocket error: " << error << std::endl;
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            auto it = std::find(m_clients.begin(), m_clients.end(), ws);
            if (it != m_clients.end()) {
                m_clients.erase(it);
            }
            m_clientCount = m_clients.size();
        });

        // Store client
        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            m_clients.push_back(ws);
            m_clientCount = m_clients.size();
        }

    } catch (const std::exception& e) {
        std::cerr << "[LibDataChannelAudioServer] Exception handling client: " << e.what() << std::endl;
    }
}

void LibDataChannelAudioServer::sendAudioData(const uint8_t* data, size_t size) {
    if (!m_running || size == 0) return;

    static int audioPacketCount = 0;
    audioPacketCount++;

    if (audioPacketCount % 100 == 0) {
        std::cout << "[LibDataChannelAudioServer] Sending audio packet " << audioPacketCount
                  << " (size: " << size << " bytes, clients: " << m_clientCount << ")" << std::endl;
    }

    std::lock_guard<std::mutex> lock(m_clientsMutex);

    // Send audio data to all connected clients
    for (auto it = m_clients.begin(); it != m_clients.end();) {
        try {
            auto webSocket = *it;
            if (webSocket && webSocket->isOpen()) {
                // Send binary data using byte* and size
                webSocket->send(reinterpret_cast<const rtc::byte*>(data), size);
                ++it;
            } else {
                // Remove invalid client
                it = m_clients.erase(it);
                m_clientCount = m_clients.size();
            }
        } catch (const std::exception& e) {
            std::cerr << "[LibDataChannelAudioServer] Error sending to client: " << e.what() << std::endl;
            it = m_clients.erase(it);
            m_clientCount = m_clients.size();
        }
    }
}
